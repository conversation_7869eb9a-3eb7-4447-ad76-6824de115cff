name: Build, Test, and Deploy SpringMath Dev to OVHCloud

on:
  push:
    branches:
      - feature/split_e2e
  pull_request:
    branches:
      - feature/split_e2e

env:
  NODE_VERSION: '22.16.0'
  METEOR_RELEASE: '3.3'

jobs:
  # Unit tests run first - fast feedback
  unit-tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        ci_node_index: [0, 1, 2]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Cache Meteor installation
        uses: actions/cache@v4
        with:
          path: ~/.meteor
          key: meteor-${{ runner.os }}-${{ env.METEOR_RELEASE }}-${{ hashFiles('app/.meteor/versions') }}
          restore-keys: |
            meteor-${{ runner.os }}-${{ env.METEOR_RELEASE }}-
            meteor-${{ runner.os }}-

      - name: Cache system dependencies
        id: cache-system-deps
        uses: actions/cache@v4
        with:
          path: |
            ~/system-deps
          key: system-deps-${{ runner.os }}-libssl1.1

      - name: Install system dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libxss1

          # Check if libssl is already cached
          if [ ! -f ~/system-deps/libssl1.1_1.1.1f-1ubuntu2_amd64.deb ]; then
            mkdir -p ~/system-deps
            wget -q http://archive.ubuntu.com/ubuntu/pool/main/o/openssl/libssl1.1_1.1.1f-1ubuntu2_amd64.deb -O ~/system-deps/libssl1.1_1.1.1f-1ubuntu2_amd64.deb
          fi
          sudo dpkg -i ~/system-deps/libssl1.1_1.1.1f-1ubuntu2_amd64.deb

      - name: Cache MongoDB binaries
        uses: actions/cache@v4
        with:
          path: ~/.mongodb-binaries
          key: mongodb-binaries-${{ runner.os }}-${{ hashFiles('app/imports/scripts/exportScores/.scripts/initMongoDb.js') }}
          restore-keys: |
            mongodb-binaries-${{ runner.os }}-

      - name: Cache schoolScrubber dependencies
        uses: actions/cache@v4
        with:
          path: app/.scripts/schoolScrubber/node_modules
          key: school-scrubber-${{ runner.os }}-${{ hashFiles('app/.scripts/schoolScrubber/package-lock.json') }}
          restore-keys: |
            school-scrubber-${{ runner.os }}-

      - name: Install Node dependencies
        working-directory: app
        run: |
          npm ci
          if [ ! -d .scripts/schoolScrubber/node_modules ]; then
            cd .scripts/schoolScrubber && npm ci
          fi

      - name: Install Meteor
        run: |
          if [ ! -e $HOME/.meteor/meteor ]; then 
            curl -sS https://install.meteor.com/?release=${{ env.METEOR_RELEASE }} | /bin/sh
          fi
          sudo ln -sf ~/.meteor/meteor /usr/local/bin/meteor

      - name: Initialize MongoDB for tests
        working-directory: app/imports/scripts/exportScores/.scripts
        run: |
          if [ ! -e ~/.mongodb-binaries ]; then
            node initMongoDb.js
          fi

      - name: Download previous unit test timing reports
        uses: dawidd6/action-download-artifact@v2
        with:
          branch: main
          name: unit-test-timing-reports
          path: app/test-results-previous
          github-token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
        continue-on-error: true

      - name: Split unit tests by timings
        id: split-tests
        uses: r7kamura/split-tests-by-timings@v0
        with:
          reports: app/test-results-previous
          glob: imports/**/*.tests.js*
          index: ${{ matrix.ci_node_index }}
          total: 3
        continue-on-error: true

      - name: Run unit tests
        working-directory: app
        run: |
          # Check if split-tests-by-timings produced output
          if [ -n "${{ steps.split-tests.outputs.paths }}" ]; then
            echo "Using timing-based test distribution"
            TEST_PATHS="${{ steps.split-tests.outputs.paths }}"
          else
            echo "Falling back to all tests (split-tests-by-timings failed or no previous data)"
            TEST_PATHS=""
          fi

          BABEL_ENV=unittesting npx jest --maxWorkers=2 --ci \
            --reporters=default --reporters=jest-junit \
            ${TEST_PATHS}
        env:
          JEST_JUNIT_OUTPUT_DIR: ./test-results
          JEST_JUNIT_OUTPUT_NAME: junit-${{ matrix.ci_node_index }}.xml

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: unit-test-results-${{ matrix.ci_node_index }}
          path: app/test-results/

      - name: Upload timing reports for future runs
        if: github.ref == 'refs/heads/main'
        uses: actions/upload-artifact@v4
        with:
          if-no-files-found: error
          name: unit-test-timing-reports
          path: app/test-results/

  # Parallel E2E tests
  e2e-tests:
    # Using ubuntu-latest (2 vCPUs, 7 GB RAM)
    # Alternative: ubuntu-latest-4-cores (4 vCPUs, 16 GB RAM) for better performance
    runs-on: ubuntu-latest
    needs: unit-tests
    strategy:
      fail-fast: false
      matrix:
        shard: [1, 2, 3, 4]

    # No MongoDB service container - using Meteor's built-in MongoDB instead

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: app/package-lock.json

      - name: Cache Cypress binary
        uses: actions/cache@v4
        with:
          path: ~/.cache/Cypress
          key: cypress-${{ runner.os }}-${{ hashFiles('app/package-lock.json') }}

      - name: Cache Meteor
        uses: actions/cache@v4
        with:
          path: |
            ~/.meteor
            app/.meteor/local
          key: meteor-e2e-${{ runner.os }}-${{ env.METEOR_RELEASE }}-${{ hashFiles('app/.meteor/versions') }}
          restore-keys: |
            meteor-e2e-${{ runner.os }}-${{ env.METEOR_RELEASE }}-

      - name: Cache system dependencies
        id: cache-system-deps
        uses: actions/cache@v4
        with:
          path: |
            ~/system-deps
          key: system-deps-${{ runner.os }}-libxss1

      - name: Install system dependencies
        run: |
          # Only update apt if we need to install something
          if ! command -v xvfb &> /dev/null || [ ! -f ~/system-deps/installed-marker ]; then
            sudo apt-get update && sudo apt-get install -y libxss1
            mkdir -p ~/system-deps
            touch ~/system-deps/installed-marker
          fi

          # Install MongoDB tools for mongorestore
          echo "=== Installing MongoDB Database Tools ==="
          if ! command -v mongorestore &> /dev/null; then
            wget -q https://fastdl.mongodb.org/tools/db/mongodb-database-tools-ubuntu2204-x86_64-100.10.0.deb -O /tmp/mongodb-tools.deb
            if [ ! -f /tmp/mongodb-tools.deb ]; then
              echo "ERROR: Failed to download MongoDB tools"
              exit 1
            fi
            
            sudo dpkg -i /tmp/mongodb-tools.deb || {
              echo "dpkg install failed, trying to fix dependencies..."
              sudo apt-get update && sudo apt-get install -f -y
              sudo dpkg -i /tmp/mongodb-tools.deb
            }
            
            # Verify mongorestore is installed
            which mongorestore && echo "✓ mongorestore installed at: $(which mongorestore)" || {
              echo "ERROR: mongorestore not found after installation"
              echo "Checking installed files:"
              dpkg -L mongodb-database-tools | grep mongorestore || echo "mongorestore not in package"
              exit 1
            }
          else
            echo "✓ mongorestore already installed at: $(which mongorestore)"
          fi

      - name: Cache schoolScrubber dependencies
        uses: actions/cache@v4
        with:
          path: app/.scripts/schoolScrubber/node_modules
          key: school-scrubber-${{ runner.os }}-${{ hashFiles('app/.scripts/schoolScrubber/package-lock.json') }}
          restore-keys: |
            school-scrubber-${{ runner.os }}-

      - name: Install dependencies
        working-directory: app
        run: |
          npm ci
          if [ ! -e $HOME/.meteor/meteor ]; then 
            curl -sS https://install.meteor.com/?release=${{ env.METEOR_RELEASE }} | /bin/sh
          fi
          sudo ln -sf ~/.meteor/meteor /usr/local/bin/meteor

      - name: Prepare test environment
        working-directory: app
        run: |
          # Copy test settings
          cp .scripts/settings-for-testing.json settings.json

      - name: Download previous test timing reports
        uses: dawidd6/action-download-artifact@v2
        with:
          branch: main
          name: cypress-timing-reports
          path: app/test-results-previous
          github-token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
        continue-on-error: true

      - name: Split tests by timings
        id: split-tests
        uses: r7kamura/split-tests-by-timings@v0
        with:
          reports: app/test-results-previous
          glob: tests/cypress/integration/**/*.spec.js
          index: ${{ matrix.shard - 1 }}
          total: 4
        continue-on-error: true

      - name: Get test specs for this shard
        id: specs
        working-directory: app
        run: |
          # Check if split-tests-by-timings produced output
          if [ -n "${{ steps.split-tests.outputs.paths }}" ]; then
            # Use the output from split-tests-by-timings
            SPECS=$(echo "${{ steps.split-tests.outputs.paths }}" | tr ' ' ',' || true)
            echo "Using timing-based test distribution"
          else
            # Fallback to the original method if split-tests-by-timings failed
            echo "Falling back to file count-based distribution"
            # Get all spec files
            ALL_SPECS=$(find tests/cypress/integration -name "*.spec.js" 2>/dev/null | sort || true)

            if [ -z "$ALL_SPECS" ]; then
              echo "No test specs found, skipping E2E tests"
              echo "specs=" >> $GITHUB_OUTPUT
              exit 0
            fi

            TOTAL=$(echo "$ALL_SPECS" | wc -l)

            # Calculate shard boundaries
            SHARD_SIZE=$(( ($TOTAL + 3) / 4 ))
            START=$(( (${{ matrix.shard }} - 1) * $SHARD_SIZE + 1 ))
            END=$(( ${{ matrix.shard }} * $SHARD_SIZE ))

            # Get specs for this shard
            SPECS=$(echo "$ALL_SPECS" | sed -n "${START},${END}p" | tr '\n' ',' || true)
          fi

          echo "specs=${SPECS%,}" >> $GITHUB_OUTPUT

          # Display for logs
          echo "Shard ${{ matrix.shard }} running specs:"
          echo "${SPECS%,}" | tr ',' '\n' | xargs -n1 basename || true

      - name: Debug system resources
        if: steps.specs.outputs.specs != ''
        run: |
          echo "=== System Resources ==="
          echo "CPU cores: $(nproc)"
          echo "Memory:"
          free -h
          echo ""
          echo "Disk space:"
          df -h /
          echo ""
          echo "=== Process list (top 10 by memory) ==="
          ps aux --sort=-%mem | head -11

      - name: Login to OVH Cloud Registry
        if: steps.specs.outputs.specs != ''
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.OVHCLOUD_REGISTRY_URL }}
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}

      - name: Extract test data from MongoDB Docker image
        if: steps.specs.outputs.specs != ''
        run: |
          echo "=== Extracting test data from MongoDB image ==="
          # Pull the image first
          docker pull ${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/spring-math-test-ci:8.0

          # Create a temporary container to extract the test data
          docker create --name temp-mongo ${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/spring-math-test-ci:8.0

          # Extract the meteor.tar file that contains the test data
          docker cp temp-mongo:/meteor.tar /tmp/meteor.tar

          # Extract the dump files
          cd /tmp
          tar -xzf meteor.tar

          # Clean up temporary container
          docker rm temp-mongo

          echo "Test data extracted to /tmp/dump/meteor/"
          ls -la /tmp/dump/meteor/ | head -10

      - name: Start Meteor application with built-in MongoDB
        if: steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Starting Meteor at $(date '+%Y-%m-%d %H:%M:%S') ==="
          echo "Using Meteor's built-in MongoDB (no external MongoDB needed)"

          # Create a log file for Meteor output
          touch meteor-startup.log

          # Start Meteor WITHOUT external MongoDB URL (uses built-in MongoDB on port 3001)
          echo "Starting Meteor with built-in MongoDB..."
          export ROOT_URL="http://localhost:3000"
          export METEOR_ENVIRONMENT=TEST
          export SERVER_WEBSOCKET_COMPRESSION=0
          export CI=true

          # Start Meteor with output to log file
          meteor --settings settings.json --port 3000 2>&1 | tee -a meteor-startup.log &

          METEOR_PID=$!
          echo "Meteor PID: $METEOR_PID"

          # Wait for MongoDB to start (watch for "=> Started MongoDB." in logs)
          echo "Waiting for MongoDB to start..."
          MONGO_WAIT=0
          while ! grep -q "=> Started MongoDB\." meteor-startup.log; do
            sleep 1
            MONGO_WAIT=$((MONGO_WAIT + 1))
            if [ $MONGO_WAIT -ge 90 ]; then
              echo "MongoDB not started after 90 seconds"
              tail -20 meteor-startup.log
              exit 1
            fi
          done
          echo "MongoDB started after ${MONGO_WAIT} seconds"

          # Give MongoDB a moment to fully initialize
          sleep 2

          # Restore test data immediately after MongoDB starts
          echo "=== Restoring test data to Meteor's MongoDB ==="

          # Check if mongorestore is available
          if ! command -v mongorestore &> /dev/null; then
            echo "ERROR: mongorestore command not found"
            echo "PATH=$PATH"
            echo "Checking for mongorestore in common locations:"
            ls -la /usr/bin/mongorestore 2>/dev/null || echo "/usr/bin/mongorestore not found"
            ls -la /usr/local/bin/mongorestore 2>/dev/null || echo "/usr/local/bin/mongorestore not found"
            exit 1
          fi

          cd /tmp/dump
          mongorestore --port 3001 --db meteor meteor/ --noIndexRestore
          echo "✓ Test data restored successfully!"

          # Now wait for Meteor app to be ready
          cd ~/work/springmath-app/springmath-app/app
          echo "Waiting for Meteor application to be ready..."
          START_TIME=$(date +%s)
          TIMEOUT=120

          while true; do
            CURRENT_TIME=$(date +%s)
            ELAPSED=$((CURRENT_TIME - START_TIME))
            
            # Check if Meteor is responding
            if curl -s http://localhost:3000 > /dev/null 2>&1; then
              echo "✓ Meteor application is ready after ${ELAPSED} seconds!"
              break
            fi
            
            # Check for timeout
            if [ $ELAPSED -ge $TIMEOUT ]; then
              echo "✗ Timeout: Meteor failed to start within ${TIMEOUT} seconds"
              echo "=== Last 50 lines of Meteor log ==="
              tail -50 meteor-startup.log
              exit 1
            fi
            
            # Show progress every 10 seconds
            if [ $((ELAPSED % 10)) -eq 0 ]; then
              echo "  Still waiting... (${ELAPSED}s elapsed)"
            fi
            
            sleep 1
          done

      - name: Run Cypress tests with resource monitoring
        if: steps.specs.outputs.specs != ''
        working-directory: app
        run: |
          echo "=== Starting Cypress tests at $(date '+%Y-%m-%d %H:%M:%S') ==="
          echo "Memory before tests:"
          free -h

          # Run Cypress with resource monitoring
          npx cypress run \
            --config-file cypress.gh.config.js \
            --spec "${{ steps.specs.outputs.specs }}" \
            --reporter junit \
            --reporter-options "mochaFile=test-results/cypress-shard-${{ matrix.shard }}-[hash].xml" &

          CYPRESS_PID=$!

          # Monitor resources during test execution
          MONITOR_INTERVAL=30
          while kill -0 $CYPRESS_PID 2>/dev/null; do
            sleep $MONITOR_INTERVAL
            echo "=== Resource check at $(date '+%H:%M:%S') ==="
            echo "Memory: $(free -h | grep "^Mem:" | awk '{print "Used: " $3 " / " $2}')"
            echo "Load average: $(uptime | awk -F'load average:' '{print $2}')"
            echo "Top 5 processes by CPU:"
            ps aux --sort=-%cpu | head -6 | tail -5 | awk '{print $2, $3, $4, $11}'
          done

          # Wait for Cypress to complete
          wait $CYPRESS_PID
          CYPRESS_EXIT_CODE=$?

          echo "=== Cypress tests completed with exit code: $CYPRESS_EXIT_CODE ==="
          echo "Memory after tests:"
          free -h

          # Check for Meteor crashes during tests
          if [ -f meteor-startup.log ]; then
            if grep -q "Error\|FATAL\|crashed" meteor-startup.log; then
              echo "=== Meteor errors detected during tests ==="
              grep -E "Error|FATAL|crashed" meteor-startup.log | tail -20
            fi
          fi

          exit $CYPRESS_EXIT_CODE
        env:
          CYPRESS_baseUrl: http://localhost:3000
          # Increase Cypress timeouts for slower environment
          CYPRESS_defaultCommandTimeout: 10000
          CYPRESS_requestTimeout: 10000
          CYPRESS_responseTimeout: 10000

      - name: Upload test artifacts
        if: always() && steps.specs.outputs.specs != ''
        uses: actions/upload-artifact@v4
        with:
          name: cypress-results-shard-${{ matrix.shard }}
          path: |
            app/cypress/screenshots/
            app/cypress/videos/
            app/test-results/
          retention-days: 14

      - name: Upload timing reports for future runs
        if: github.ref == 'refs/heads/main' && steps.specs.outputs.specs != ''
        uses: actions/upload-artifact@v4
        with:
          if-no-files-found: error
          name: cypress-timing-reports
          path: app/test-results/

  # Build and deploy only after all tests pass (on push to dev)
  build-and-deploy:
    if: github.event_name == 'push' && github.ref == 'refs/heads/dev'
    needs: [unit-tests, e2e-tests]
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Login to OVH Harbor Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.OVHCLOUD_REGISTRY_URL }}
          username: ${{ vars.OVHCLOUD_REGISTRY_USERNAME }}
          password: ${{ secrets.OVHCLOUD_REGISTRY_ACCESS_TOKEN }}

      - name: Kubernetes set context
        uses: Azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.OVHCLOUD_TEST_KUBE_CONFIG }}

      - name: Docker Build and Push, Kubernetes apply
        run: |
          # Get the current version from the version.js file
          CURRENT_VERSION=$(grep -o '"[0-9]\+\.[0-9]\+\.[0-9]\+"' app/imports/startup/client/version.js | tr -d '"')
          echo "Current version: $CURRENT_VERSION"

          # Use git commit short hash as build number for uniqueness
          BUILD_NUMBER=$(git rev-parse --short HEAD)
          echo "Build number: $BUILD_NUMBER"

          # Create release tag with build number for dev environment
          export RELEASE="${CURRENT_VERSION}-${BUILD_NUMBER}-dev"
          echo "Release tag: $RELEASE"

          # split the pieces
          REGISTRY_REPO="${{ vars.OVHCLOUD_REGISTRY_URL }}/${{ vars.OVHCLOUD_REGISTRY_PROJECT_NAME }}/springmath-dev"

          docker build -t "$REGISTRY_REPO:$RELEASE" .
          docker push  "$REGISTRY_REPO:$RELEASE"

          # correct way to add 'latest'
          docker tag  "$REGISTRY_REPO:$RELEASE" "$REGISTRY_REPO:latest"
          docker push "$REGISTRY_REPO:latest"

          # set up sm secrets - encode plain text vars to base64
          echo 'Setting up Kubernetes secrets...'
          DEV_ROOT_URL_B64=$(echo -n "${{ vars.DEV_ROOT_URL }}" | base64 -w 0)
          DEV_PORT_B64=$(echo -n "${{ vars.METEOR_PORT }}" | base64 -w 0)

          sed -i'' -e "s|DEV_ROOT_URL_SECRET|${DEV_ROOT_URL_B64}|g" \
            -e "s|DEV_MONGO_URL_SECRET|${{ secrets.DEV_MONGO_URL }}|g" \
            -e "s|DEV_MONGO_OPLOG_URL_SECRET|${{ secrets.DEV_MONGO_OPLOG_URL }}|g" \
            -e "s|DEV_PORT_SECRET|${DEV_PORT_B64}|g" \
            -e "s|DEV_METEOR_SETTINGS_SECRET|${{ secrets.DEV_METEOR_SETTINGS }}|g" \
            ./k8/dev.yml

          # set up docker creds
          export DOCKER_CONFIG=$(cat ~/.docker/config.json | base64 -w 0)
          sed -i'' -e "s|IMAGE_FULL_TAG|$REGISTRY_REPO:$RELEASE|g" \
            -e "s|DOCKER_CONFIG|$DOCKER_CONFIG|g" \
            ./k8/dev.yml

          echo 'applying yml file'

          # apply test
          kubectl apply -f ./k8/dev.yml

           # Wait for deployment to complete
          kubectl rollout status deployment/springmath-dev --timeout=600s

      - name: Purge Cloudflare cache
        uses: jakejarvis/cloudflare-purge-action@master
        env:
          CLOUDFLARE_ZONE: ${{ vars.SPRINGMATH_CLOUDFLARE_ZONE_ID }}
          CLOUDFLARE_TOKEN: ${{ secrets.SPRINGMATH_CLOUDFLARE_PURGE_TOKEN }}
          PURGE_URLS: '["https://app.dev.springmath.com/*"]'
