import { compare as bcryptCompare } from "bcrypt";

import { Meteor } from "meteor/meteor";
import { Accounts } from "meteor/accounts-base";
import { check, Match } from "meteor/check";
import { difference, escapeRegExp, findIndex, get, groupBy, isEqual, range, uniq } from "lodash";
import { Email } from "meteor/email";
import { SHA256 } from "meteor/sha";

import {
  getUpdatedCoachProfile,
  getUpdatedTeacherProfile,
  getUserSiteAccess,
  isUserConfiguringMFA,
  shouldForceUserToChangePassword
} from "./utilities";
import { Users } from "../users";
import { StudentGroups } from "../../studentGroups/studentGroups";
import { Sites } from "../../sites/sites";
import { Organizations } from "../../organizations/organizations";
import * as auth from "../../authorization/server/methods";
import { Roles } from "../../roles/roles";
import * as utils from "../../utilities/utilities";
import {
  capitalizeF<PERSON>t<PERSON>etter,
  getCurrentSchoolYear,
  getMeteor<PERSON>ser,
  getMeteorUserId,
  getV<PERSON>uesByKey,
  mapReplaceAll
} from "../../utilities/utilities";
// eslint-disable-next-line import/no-cycle
import { getGradesWithGroupsForYears } from "../../grades/methods";
import { getCurrentLoggedInUserRole } from "../../roles/server/methods";
import { getTimestampInfo } from "../../helpers/getTimestampInfo";
import { ROLE_IDS } from "../../../../tests/cypress/support/common/constants";

const teacherRole = "arbitraryIdteacher";
const adminRole = "arbitraryIdadmin";
const dataAdminRole = "arbitraryIddataAdmin";
const coachRole = "arbitraryIdadmin";

export async function insertUser(newUser, customUserId) {
  const isCoach = newUser.role === "arbitraryIdadmin";
  const normalizedEmail = normalizeEmail(newUser.email);
  const query =
    isCoach || !newUser.localId
      ? {
          "profile.orgid": newUser.orgid,
          "emails.address": normalizedEmail
        }
      : {
          "profile.orgid": newUser.orgid,
          $or: [{ "profile.localId": newUser.localId }, { "emails.address": normalizedEmail }]
        };
  const existingUsers = await Users.find(query).fetchAsync();
  const existingUser =
    existingUsers.find(elem => normalizeEmail(elem.emails[0].address) === normalizedEmail) || existingUsers[0];
  const lastModified = await getTimestampInfo(customUserId || "ObserverOnServer", newUser.orgid);
  if (!existingUser) {
    const siteAccess = getUserSiteAccess(newUser);
    const profile = {
      onboarded: false,
      orgid: newUser.orgid,
      localId: newUser.localId,
      siteAccess,
      name: newUser.name,
      created: newUser.created,
      lastModified: newUser.created,
      rosterImportId: newUser.rosterImportId
    };
    const user = {
      email: normalizedEmail,
      password: newUser.password,
      profile
    };
    if (!user.password) {
      delete user.password;
    }
    return createUserStandard(user);
  }
  // console.log("User already exists... update");
  let updatedEmail = null;
  let updatedLocalId = null;
  let profile;
  const currentSchoolYear = newUser.schoolYear;
  if (isCoach) {
    profile = getUpdatedCoachProfile(newUser, existingUser.profile, currentSchoolYear, lastModified);
  } else {
    // did the email change?
    if (normalizeEmail(existingUser.emails[0].address) !== normalizedEmail) {
      updatedEmail = normalizedEmail;
    }
    // did the localId change?
    if (existingUser.profile.localId !== newUser.localId) {
      updatedLocalId = newUser.localId;
    }
    profile = getUpdatedTeacherProfile(newUser, existingUser, currentSchoolYear, lastModified);
  }
  return updateUserStandard({ userId: existingUser._id, profile, email: updatedEmail, localId: updatedLocalId });
}

export function normalizeEmail(email) {
  return email.trim().toLowerCase();
}

export async function createUserStandard(user) {
  check(user, Object);
  return Accounts.createUserAsync(user);
}

export async function updateUserStandard({ userId, profile, email, localId }) {
  check(userId, String);
  check(profile, Object);
  check(email, Match.Maybe(String));
  check(localId, Match.Maybe(String));
  Users.validate({
    profile
  });
  const lastModified = await getTimestampInfo(userId, null, "updateUserStandard");
  if (email) {
    const user = await Users.findOneAsync({ "emails.address": email });
    if (user && user._id) {
      const { last, first } = user.profile.name;
      throw new Meteor.Error(409, `This email is already used by: ${last}, ${first}, ID: ${user._id}`);
    }
    await Users.updateAsync(userId, {
      $set: {
        "emails.0.address": email,
        "profile.lastModified": lastModified
      }
    });
  }
  if (localId) {
    const user = await Users.findOneAsync({ "profile.orgid": profile.orgid, "profile.localId": localId });
    if (user && user._id) {
      const { last, first } = user.profile.name;
      throw new Meteor.Error(409, `This teacher id is already used by: ${last}, ${first}, ID: ${user._id}`);
    }
    // eslint-disable-next-line no-param-reassign
    profile.localId = localId;
  }
  await Users.updateAsync(userId, {
    $set: {
      profile: { ...profile, lastModified }
    }
  });
  return userId;
}

async function addTestPasswordToUser(userId) {
  const lastModified = await getTimestampInfo(userId, null, "addTestPasswordToUser");
  await Users.updateAsync(
    { _id: userId },
    {
      $set: {
        services: {
          password: {
            bcrypt: "$2a$10$/a3ZYVws0EbfU7AzXDJIA.43tCYkSKtx33OKBPjKyv76DZOSQl596"
          },
          resume: {
            loginTokens: []
          }
        },
        "profile.lastModified": lastModified
      }
    }
  );
}

export async function sendEnrollmentEmail(userId) {
  if (process.env.METEOR_ENVIRONMENT === "TEST") {
    await addTestPasswordToUser(userId);
  } else {
    const user = await Users.findOneAsync({ _id: userId }, { fields: { "services.password.enroll": 1 } });
    if (user?.services?.password?.enroll) {
      const { token, email, when, reason } = user.services.password.enroll;
      console.log(`Initial Enroll Item: 
        {
          token: ${token}
          email: ${email}
          when: ${when}
          reason: ${reason}
        }
      `);
    }
    const enrollDoc = await Accounts.sendEnrollmentEmail(userId);

    // NOTE(fmazur) - Debug logs for SPRIN-2647
    if (enrollDoc) {
      const userEnrollItem = enrollDoc.user.services?.password?.enroll;
      const userEnrollLog = userEnrollItem
        ? `User Enroll Item: 
        {
          token: ${userEnrollItem.token}
          email: ${userEnrollItem.email}
          when: ${userEnrollItem.when}
          reason: ${userEnrollItem.reason}
        }`
        : "";
      // eslint-disable-next-line no-console
      console.log(`
      User ${userId} enrollment
        URL: ${enrollDoc.url}
        TOKEN: ${enrollDoc.token}
        ${userEnrollLog}
    `);
    }
  }
}

export async function changePrimaryRole({ roleObject, userId, shouldClearSchoolYear }) {
  const user = await Users.findOneAsync({ _id: userId });
  const newPrimaryRoleIndex = findIndex(
    user.profile.siteAccess,
    sa => sa.role === roleObject.role && sa.siteId === roleObject.siteId
  );
  if (newPrimaryRoleIndex < 0) {
    throw new Meteor.Error(403, "You are not authorized to change to this role.");
  }

  // the two lines below shift the new primary role to the first element of the siteAccess array
  const newPrimaryRole = user.profile.siteAccess.splice(newPrimaryRoleIndex, 1)[0];
  user.profile.siteAccess.unshift(newPrimaryRole);
  const lastModified = await getTimestampInfo(userId, null, "changePrimaryRole");
  const setQuery = { $set: { "profile.siteAccess": user.profile.siteAccess, "profile.lastModified": lastModified } };
  if (shouldClearSchoolYear) {
    setQuery.$unset = { "profile.selectedSchoolYear": "" };
  }
  await Users.updateAsync({ _id: user._id }, setQuery);
  return true;
}

async function createDataAdminUserAndSendEnrollmentLink({
  orgid,
  siteId = "allSites",
  email,
  localId = "",
  firstName,
  lastName
}) {
  check(orgid, String);
  check(siteId, String);
  check(email, String);
  check(localId, String);
  check(firstName, String);
  check(lastName, String);
  const daRole = await Roles.findOneAsync({
    name: "dataAdmin"
  });
  const bdo = await getTimestampInfo(getMeteorUserId(), orgid);
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid,
      siteAccess: [
        {
          role: daRole._id,
          siteId,
          schoolYear: 0,
          isActive: true,
          isDefault: true
        }
      ],
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: bdo,
      lastModified: bdo
    }
  };
  if (localId) {
    opts.profile.localId = localId;
  }
  const userId = await Accounts.createUserAsync(opts);
  const org = await Organizations.findOneAsync({ _id: orgid });
  if (!org?.useSSOOnly) {
    await sendEnrollmentEmail(userId);
  }
  return { actionTaken: "created" };
}

const filterAndSortSiteIdsBySiteName = async (orgid, siteIds) => {
  return (await Sites.find({ orgid, _id: { $in: siteIds } }, { sort: { name: 1 }, _id: 1 }).fetchAsync()).map(
    site => site._id
  );
};

async function getUniqSchoolYearsForSite(siteId) {
  const studentGroups = await StudentGroups.find({ siteId }, { fields: { schoolYear: 1 } }).fetchAsync();
  const uniqSchoolYearsForSiteFromStudentGroups = utils.uniqUnsafe(studentGroups || [], sg => sg.schoolYear, true);
  if (uniqSchoolYearsForSiteFromStudentGroups.length) {
    return uniqSchoolYearsForSiteFromStudentGroups;
  }
  const siteSchoolYear = await Sites.findOneAsync(siteId, { fields: { schoolYear: 1 } });
  return [siteSchoolYear.schoolYear];
}

export async function updateCoachesForSiteWithNewSchoolYears(siteId, schoolYears) {
  check(siteId, String);
  check(schoolYears, [Number]);

  const aRole = await Roles.findOneAsync({
    name: "admin"
  });
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "updateCoachesForSiteWithNewSchoolYears");
  // for now just do one at a time.
  // Most sites only have 0-2 coaches, no need to bulk update, etc.
  // find all users at the site with the role admin...
  const users = await Users.find({ siteId }).fetchAsync();
  let updateCount = 0;
  const updateCounts = await Promise.all(
    users.map(async user => {
      if (!(schoolYears && schoolYears.length && user.profile)) {
        return 0;
      }
      const siteAccesses = schoolYears.reduce(
        (a, c) =>
          !a.some(siteAccess => siteAccess.schoolYear === c)
            ? [
                ...a,
                {
                  role: aRole._id,
                  siteId,
                  schoolYear: c,
                  isActive: true,
                  isDefault: false // don't think we're leveraging this...
                }
              ]
            : a,
        user.profile.siteAccess
      );
      return Users.updateAsync(
        { _id: user._id },
        { $set: { "profile.siteAccess": siteAccesses, "profile.lastModified": lastModified } }
      );
    })
  );
  if (updateCounts.length) {
    updateCount = updateCounts.reduce((sum, count) => sum + count, 0);
  }
  return updateCount;
}

export async function createCoachUserAndSendEnrollmentLink({
  orgid,
  siteIds,
  email,
  localId = "",
  firstName,
  lastName
}) {
  check(orgid, String);
  check(siteIds, [String]);
  check(email, String);
  check(localId, String);
  check(firstName, String);
  check(lastName, String);
  const aRole = await Roles.findOneAsync({
    name: "admin"
  });
  const bdo = await getTimestampInfo(getMeteorUserId(), orgid);
  // find range of schoolYears for the site currently
  let siteAccess = [];
  const processedSiteIds = await filterAndSortSiteIdsBySiteName(orgid, siteIds);
  const yearsForSites = await Promise.all(
    processedSiteIds.map(async siteId => {
      const uniqSchoolYears = await getUniqSchoolYearsForSite(siteId);
      return uniqSchoolYears.map(schoolYear => ({
        role: aRole._id,
        siteId,
        schoolYear,
        isActive: true,
        isDefault: true
      }));
    })
  );
  siteAccess = yearsForSites.flat(1);
  if (!siteAccess.length) {
    throw new Meteor.Error(
      409,
      "There was a problem with adding this coach, it seems he wasn't granted any valid site access."
    );
  }
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid,
      siteAccess,
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: bdo,
      lastModified: bdo
    }
  };
  if (localId) {
    opts.profile.localId = localId;
  }
  const userId = await Accounts.createUserAsync(opts);
  const org = await Organizations.findOneAsync({ _id: orgid });
  if (!org?.useSSOOnly) {
    await sendEnrollmentEmail(userId);
  }
  return true;
}

async function addDataAdminAccessToAdmin({ user, localId = "", orgid, siteId = "allSites" }) {
  const result = { wasUserAccessChanged: false };
  if (user.profile.siteAccess.find(sa => sa.role === "arbitraryIddataAdmin")) {
    return result;
  }
  if (user.profile.siteAccess.every(sa => sa.role !== "arbitraryIdadmin")) {
    const errorMessage = `The same email ${user.emails[0].address} cannot be used to create both Teacher and Data Amin roles. Only Coach and Data Admin roles assigned to the same email address is allowed.`;
    throw new Meteor.Error(errorMessage);
  }
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  const newSiteAccess = [...user.profile.siteAccess];
  const dataAdminAccess = newSiteAccess.find(sa => sa.role === "arbitraryIddataAdmin");
  if (!dataAdminAccess) {
    newSiteAccess.push({
      role: "arbitraryIddataAdmin",
      siteId,
      schoolYear: currentSchoolYear,
      isDefault: true,
      isActive: true
    });
    result.wasUserAccessChanged = true;
  }
  const lastModified = await getTimestampInfo(user?._id, orgid, "addDataAdminAccessToAdmin");
  const setQuery = { "profile.siteAccess": newSiteAccess, "profile.lastModified": lastModified };
  if (localId) {
    setQuery["profile.localId"] = localId;
  }
  await Users.updateAsync({ _id: user._id }, { $set: setQuery });
  return result;
}

async function removeUsers(userIds) {
  await Users.removeAsync({ _id: { $in: userIds } });
  return true;
}

async function modifySiteAccess({ currentUserId, userId, siteAccess, orgid, role = ROLE_IDS.admin }) {
  const currentUser = await Users.findOneAsync({ _id: currentUserId });
  const currentSiteAccess = (await Users.findOneAsync({ _id: userId }))?.profile.siteAccess;
  if (!currentSiteAccess) {
    return false;
  }
  const usedRoles = [];
  const schoolYear = await getCurrentSchoolYear(currentUser, orgid);
  const newSiteAccess = siteAccess
    .map(obj => {
      if (!usedRoles.includes(obj.role)) {
        usedRoles.push(obj.role);
      }
      if (obj.role !== role || (obj.role === role && !obj.isActive)) {
        return null;
      }
      if (role === ROLE_IDS.dataAdmin) {
        return {
          role: ROLE_IDS.dataAdmin,
          siteId: obj.siteId,
          schoolYear: 0,
          isActive: true,
          isDefault: true
        };
      }
      return {
        ...obj,
        schoolYear,
        isDefault: true
      };
    })
    .filter(sa => sa);
  currentSiteAccess.forEach(obj => {
    if (!usedRoles.includes(obj.role)) {
      usedRoles.push(obj.role);
    }
    if (obj.role !== role) {
      newSiteAccess.push(obj);
    }
  });
  const lastModified = await getTimestampInfo(userId, orgid, "modifySiteAccess");
  await Users.updateAsync(
    { _id: userId },
    { $set: { "profile.siteAccess": newSiteAccess, "profile.lastModified": lastModified } }
  );
  return true;
}

export async function removeCoachUser(coachId, siteId) {
  const coachData = await Users.findOneAsync({ _id: coachId });
  if (!coachData) {
    throw new Meteor.Error("removeCoachUser", "Coach with this ID doesn't exist.");
  }
  const schoolYear = await utils.getCurrentSchoolYear(coachData, coachData.orgid);
  const newSiteAccess = coachData.profile.siteAccess.filter(siteAccess => siteAccess.siteId !== siteId);
  const isUserAssignedToAnyGroup = await utils.isUserAssignedToAnyGroup({ siteId, schoolYear, userId: coachId });
  if (isUserAssignedToAnyGroup) {
    newSiteAccess.push({
      role: "arbitraryIdteacher",
      siteId,
      schoolYear,
      isDefault: true,
      isActive: true
    });
  }
  if (!newSiteAccess.length) {
    return Users.removeAsync({ _id: coachId });
  }

  const lastModified = await getTimestampInfo(getMeteorUserId(), coachData.profile.orgid, "removeCoachUser");
  return Users.updateAsync(
    { _id: coachId },
    { $set: { "profile.siteAccess": newSiteAccess, "profile.lastModified": lastModified } }
  );
}

export async function removeDataAdminUsers(dataAdminIds) {
  const dataAdminDocuments = await Users.find({ _id: { $in: dataAdminIds } }).fetchAsync();
  if (!dataAdminDocuments.length) {
    throw new Meteor.Error("removeDataAdminUsers", "Selected Data Administrator(s) do not exist.");
  }

  const parsedDataAdminDocuments = dataAdminDocuments.reduce((documents, document) => {
    const newSiteAccess = document.profile.siteAccess.filter(siteAccess => siteAccess.role !== "arbitraryIddataAdmin");
    if (!newSiteAccess.length) {
      Users.removeAsync({ _id: document._id });
    } else {
      documents.push({
        _id: document._id,
        siteAccess: newSiteAccess,
        orgid: document.profile.orgid
      });
    }
    return documents;
  }, []);

  await Promise.all(
    parsedDataAdminDocuments.map(async doc => {
      const lastModified = await getTimestampInfo(getMeteorUserId(), doc.orgid, "removeDataAdminUsers");
      await Users.updateAsync(
        { _id: doc._id },
        { $set: { "profile.siteAccess": doc.siteAccess, "profile.lastModified": lastModified } }
      );
    })
  );
}

export async function createSupportUserAndSendEnrollmentLink({ email, firstName, lastName, organizationIds }) {
  const aRole = await Roles.findOneAsync({
    name: "support"
  });
  const bdo = await getTimestampInfo(getMeteorUserId());
  const schoolYears = await utils.getAllAvailableSchoolYears();

  const siteAccess = schoolYears.map(schoolYear => ({
    role: aRole._id,
    siteId: "allSites",
    schoolYear,
    isActive: true,
    isDefault: true
  }));
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid: "allOrgs",
      siteAccess,
      organizationAccess: organizationIds,
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: bdo,
      lastModified: bdo
    }
  };

  const userId = await createUserAndReturnUserId(opts);
  await sendEnrollmentEmail(userId);
  return true;
}

async function createUserAndReturnUserId(opts) {
  const user = await Users.findOneAsync({ email: opts.email });
  const errorMessage = "User with this email already exists";
  let userId;
  if (user) {
    throw new Meteor.Error("createUserAndReturnUserId", errorMessage);
  }
  try {
    userId = await Accounts.createUserAsync(opts);
  } catch (e) {
    throw new Meteor.Error("createUserAndReturnUserId", errorMessage);
  }

  return userId;
}

export async function createUniversalCoachAndSendEnrollmentLink({ email, firstName, lastName }) {
  const aRole = await Roles.findOneAsync({
    name: "universalCoach"
  });
  const timestampInfo = await getTimestampInfo(getMeteorUserId());
  const schoolYears = await utils.getAllAvailableSchoolYears();

  const siteAccess = schoolYears.map(schoolYear => ({
    role: aRole._id,
    siteId: "allSites",
    schoolYear,
    isActive: true,
    isDefault: true
  }));
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid: "allOrgs",
      siteAccess,
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: timestampInfo,
      lastModified: timestampInfo
    }
  };
  const userId = await createUserAndReturnUserId(opts);
  await sendEnrollmentEmail(userId);
  return true;
}

async function createUniversalDataAdminAndSendEnrollmentLink({ email, firstName, lastName }) {
  check(email, String);
  check(firstName, String);
  check(lastName, String);
  const daRole = await Roles.findOneAsync({
    name: "universalDataAdmin"
  });
  const bdo = await getTimestampInfo(getMeteorUserId());
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid: "allOrgs",
      siteAccess: [
        {
          role: daRole._id,
          siteId: "allSites",
          schoolYear: 0,
          isActive: true,
          isDefault: true
        }
      ],
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: bdo,
      lastModified: bdo
    }
  };
  const userId = await createUserAndReturnUserId(opts);
  await sendEnrollmentEmail(userId);
  return true;
}

async function createSuperAdminAndSendEnrollmentLink({ email, firstName, lastName }) {
  check(email, String);
  check(firstName, String);
  check(lastName, String);
  const saRole = await Roles.findOneAsync({
    name: "superAdmin"
  });
  const bdo = await getTimestampInfo(getMeteorUserId());
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid: "allOrgs",
      siteAccess: [
        {
          role: saRole._id,
          siteId: "allSites",
          schoolYear: 0,
          isActive: true,
          isDefault: true
        }
      ],
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: bdo,
      lastModified: bdo
    }
  };
  const userId = await createUserAndReturnUserId(opts);
  await sendEnrollmentEmail(userId);
  return true;
}

async function createDownloaderAndSendEnrollmentLink({ email, firstName, lastName }) {
  check(email, String);
  check(firstName, String);
  check(lastName, String);
  const downloaderRole = await Roles.findOneAsync({
    name: "downloader"
  });
  const bdo = await getTimestampInfo(getMeteorUserId());
  const opts = {
    email,
    profile: {
      onboarded: false,
      orgid: "allOrgs",
      siteAccess: [
        {
          role: downloaderRole._id,
          siteId: "allSites",
          schoolYear: 0,
          isActive: true,
          isDefault: true
        }
      ],
      name: {
        first: firstName,
        last: lastName,
        middle: null
      },
      created: bdo,
      lastModified: bdo
    }
  };
  const userId = await createUserAndReturnUserId(opts);
  await sendEnrollmentEmail(userId);
  return true;
}

async function updateSupportUser(userId, { organizationAccess }) {
  const lastModified = await getTimestampInfo(userId, null, "updateSupportUser");
  await Users.updateAsync(
    { _id: userId },
    {
      $set: {
        "profile.organizationAccess": organizationAccess,
        "profile.lastModified": lastModified
      }
    }
  );
  return true;
}

export const getAllSchoolYearsFor = async user => {
  const firstSpringMathYear = 2013;
  const latestAvailableSchoolYear = await utils.getLatestAvailableSchoolYear(user);
  return range(firstSpringMathYear, latestAvailableSchoolYear + 1);
};

export async function updateCoachUserSiteAccess({ user, localId = "", siteIds }) {
  check(user, Object);
  check(localId, String);
  check(siteIds, [String]);
  let updatedSiteAccess = [];
  const result = { wasUserAccessChanged: false };
  const isTeacher = user.profile.siteAccess.some(sa => sa.role === ROLE_IDS.teacher);
  if (isTeacher) {
    // Upgrade teacher to admin
    user.profile.siteAccess.forEach(sa => {
      if (sa.role === ROLE_IDS.teacher && siteIds.includes(sa.siteId)) {
        updatedSiteAccess.push({ ...sa, role: ROLE_IDS.admin });
      } else {
        updatedSiteAccess.push(sa);
      }
    });
  } else {
    updatedSiteAccess = [...user.profile.siteAccess];
  }
  const { orgid } = user.profile;
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  const processedSiteIds = await filterAndSortSiteIdsBySiteName(orgid, siteIds);
  await Promise.all(
    processedSiteIds.map(async siteId => {
      const adminRoleForSite = updatedSiteAccess.find(
        sa => sa.siteId === siteId && sa.schoolYear === currentSchoolYear && sa.role === ROLE_IDS.admin
      );
      if (!adminRoleForSite) {
        const allSchoolYears = await getAllSchoolYearsFor(user);
        const yearsWithGrades = await getGradesWithGroupsForYears(siteId, allSchoolYears);
        Object.entries(yearsWithGrades).forEach(([year, grades]) => {
          if (grades.length) {
            updatedSiteAccess.unshift({
              role: ROLE_IDS.admin,
              siteId,
              schoolYear: parseInt(year),
              isDefault: true,
              isActive: true
            });
          }
        });
        result.wasUserAccessChanged = true;
      }
    })
  );
  const setQuery = {
    "profile.siteAccess": updatedSiteAccess,
    "profile.lastModified": await getTimestampInfo(getMeteorUserId(), orgid, "updateCoachUserSiteAccess")
  };
  if (localId) {
    setQuery["profile.localId"] = localId;
  }
  await Users.updateAsync(
    { _id: user._id },
    {
      $set: setQuery
    }
  );
  return result;
}
export async function getUserInviteEmailStatus(usersIds) {
  const users = Users.find({ _id: { $in: usersIds } });
  const inviteSentStatusByUserId = {};

  await users.forEachAsync(user => {
    inviteSentStatusByUserId[user._id] = !!(
      get(user, "services.password.enroll.token") || get(user, "services.password.bcrypt")
    );
  });
  return inviteSentStatusByUserId;
}

async function isLocalIdUniqueInOrg({ localId, orgid, userId = "" }) {
  const existingUser = await Users.findOneAsync(
    { _id: { $ne: userId }, "profile.localId": localId, "profile.orgid": orgid },
    { fields: { _id: 1 } }
  );
  return !existingUser;
}

async function updateCoachUser({ coachEmail, localId, orgid, siteIds }) {
  const existingUser = await Users.findOneAsync({
    "emails.address": { $regex: new RegExp(`^${escapeRegExp(coachEmail)}$`, "i") }
  });
  if (!existingUser) {
    throw new Meteor.Error(
      403,
      "Unexpected error when trying to match a user with the provided email address. Please try again later"
    );
  }

  if (await auth.hasAccess(["dataAdmin"], { userId: existingUser._id, orgid })) {
    const { wasUserAccessChanged } = await updateCoachUserSiteAccess({ user: existingUser, localId, siteIds });
    const dataAdminRoleAccess = existingUser.profile.siteAccess.find(sa => sa.role === "arbitraryIddataAdmin");
    await changePrimaryRole({ roleObject: dataAdminRoleAccess, userId: existingUser._id });
    return {
      actionTaken: wasUserAccessChanged ? "updated" : null
    };
  }

  if (
    await auth.hasAccess(["superAdmin", "support", "universalCoach"], {
      userId: existingUser._id,
      orgid
    })
  ) {
    throw new Meteor.Error(
      403,
      "The provided email address already belongs to a user with higher privileges than a coach account"
    );
  }
  if (existingUser.profile.orgid !== orgid) {
    throw new Meteor.Error(
      403,
      "The provided email address already belongs to a user that is assigned to a different organization"
    );
  }
  return {
    actionTaken: (await updateCoachUserSiteAccess({ user: existingUser, localId, siteIds })).wasUserAccessChanged
      ? "updated"
      : null
  };
}

export async function removeGroupOwnershipAndDeactivateSiteAccess(userIds = [], siteId) {
  if (!userIds.length) {
    return true;
  }
  const schoolYear = await utils.getCurrentSchoolYear(await getMeteorUser());
  const lastModified = await getTimestampInfo(
    getMeteorUserId() || "",
    null,
    "removeGroupOwnershipAndDeactivateSiteAccess"
  );

  await StudentGroups.updateAsync(
    {
      siteId,
      isActive: true,
      schoolYear,
      $or: [{ ownerIds: { $in: userIds } }, { secondaryTeachers: { $in: userIds } }]
    },
    { $pull: { ownerIds: { $in: userIds }, secondaryTeachers: { $in: userIds } }, $set: { lastModified } },
    { multi: true }
  );
  const users = await Users.find(
    { _id: { $in: userIds } },
    { fields: { "profile.siteAccess": 1, "profile.orgid": 1 } }
  ).fetchAsync();
  await deactivateUsersSiteAccess(users, siteId, schoolYear);
  return true;
}

export async function deactivateUsersSiteAccess(users, siteId, schoolYear) {
  const userIds = users.map(u => u._id);

  await Promise.all(
    userIds.map(async userId => {
      const user = await Users.findOneAsync({ _id: userId });
      if (user) {
        const isAnOwnerOrSecondaryTeacher = await StudentGroups.findOneAsync(
          { $or: [{ ownerIds: { $in: [user._id] } }, { secondaryTeachers: { $in: [user._id] } }], siteId, schoolYear },
          { ownerIds: 1, secondaryTeachers: 1 }
        );
        const siteAccessArray = user.profile.siteAccess.map(siteAccess => {
          const shouldVerifySchoolYear =
            siteAccess.role !== adminRole ? siteAccess.schoolYear === schoolYear || siteAccess.schoolYear === 0 : true;
          const shouldDeactivateSiteAccess =
            siteAccess.siteId === siteId && !isAnOwnerOrSecondaryTeacher && shouldVerifySchoolYear;
          if (shouldDeactivateSiteAccess) {
            return { ...siteAccess, isActive: false };
          }
          return siteAccess;
        });
        const lastModified = await getTimestampInfo(getMeteorUserId(), null, "deactivateUsersSiteAccess");
        await Users.updateAsync(
          { _id: userId },
          {
            $set: {
              "profile.siteAccess": siteAccessArray,
              "profile.lastModified": lastModified
            }
          }
        );
      }
    })
  );
}

function divideUsersByRoleForGivenSchoolYear(orgUsers, currentSchoolYear) {
  return ["arbitraryIdteacher", "arbitraryIdadmin", "arbitraryIddataAdmin"].reduce((usersByRoleId, roleId) => {
    // eslint-disable-next-line no-param-reassign
    usersByRoleId[roleId] = orgUsers.filter(user => {
      if (!user.profile.siteAccess.length && roleId === "arbitraryIdadmin") {
        return true;
      }
      return user.profile.siteAccess.find(sa => {
        if (currentSchoolYear !== "all") {
          return sa.role === roleId && (sa.schoolYear === currentSchoolYear || sa.schoolYear === 0);
        }
        return sa.role === roleId;
      });
    });
    return usersByRoleId;
  }, {});
}

async function getActiveOrgGroupsByOwners(orgid, schoolYear, ownerIds, studentGroupId) {
  return StudentGroups.find(
    {
      ...(studentGroupId && { _id: studentGroupId }),
      orgid,
      isActive: true,
      schoolYear,
      $or: [{ ownerIds: { $in: ownerIds } }, { secondaryTeachers: { $in: ownerIds } }]
    },
    { fields: { secondaryTeachers: 1, ownerIds: 1, siteId: 1 } }
  ).fetchAsync();
}

function getUserIdsWithSiteAccess({ users, role, siteId, isActive }) {
  return users
    .filter(user =>
      user.profile.siteAccess.find(
        sa => sa.role === role && sa.siteId === siteId && sa.isActive === isActive // Filter out inactive from group owners
      )
    )
    .map(u => u._id);
}

export async function getArchivedTeacherOrAdminIds(userIds = [], orgid) {
  const users = await Users.find({ _id: { $in: userIds } }, { fields: { "profile.siteAccess": 1 } }).fetchAsync();
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);

  const archivedTeacherOrAdminIds = await Promise.all(
    users.map(async user => {
      const siteAccessSiteIdsInCurrentSchoolYear = user.profile.siteAccess
        .filter(sa => sa.siteId && sa.schoolYear === currentSchoolYear && sa.isActive === true)
        .map(s => s.siteId);
      if (!siteAccessSiteIdsInCurrentSchoolYear.length) {
        return user._id;
      }
      const studentGroupWithOwner = await StudentGroups.findOneAsync(
        {
          siteId: { $in: siteAccessSiteIdsInCurrentSchoolYear },
          $or: [{ ownerIds: user._id }, { secondaryTeachers: user._id }],
          isActive: true,
          schoolYear: currentSchoolYear
        },
        { _id: 1 }
      );
      if (!studentGroupWithOwner) {
        return user._id;
      }
      return undefined;
    })
  );
  return archivedTeacherOrAdminIds.filter(Boolean);
}

export async function getManageTeacherData({ orgid, siteId, studentGroupId }) {
  const orgUsers = await Users.find({
    "profile.orgid": orgid,
    $or: [
      { "profile.siteAccess.role": { $in: [teacherRole, adminRole, dataAdminRole] } },
      { "profile.siteAccess": [] }
    ],
    ...(siteId && { "profile.siteAccess.siteId": siteId })
  }).fetchAsync();
  const siteIds = (await Sites.find({ orgid }, { fields: { _id: 1 } }).fetchAsync()).map(s => s._id);
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser(), orgid);
  const { arbitraryIdteacher, arbitraryIdadmin, arbitraryIddataAdmin } = divideUsersByRoleForGivenSchoolYear(
    orgUsers,
    "all"
  );
  const adminUserIdsWithoutSiteAccess = arbitraryIdadmin.filter(u => !u.profile.siteAccess.length).map(u => u._id);
  const activeUserIdsByRoleBySiteId = {};
  const archivedUserIdsByRoleBySiteId = {};

  await Promise.all(
    Object.entries({
      arbitraryIdteacher,
      arbitraryIdadmin
    }).map(async ([role, users]) => {
      if (!activeUserIdsByRoleBySiteId[role]) {
        activeUserIdsByRoleBySiteId[role] = {};
      }
      if (!archivedUserIdsByRoleBySiteId[role]) {
        archivedUserIdsByRoleBySiteId[role] = {};
      }

      const userIds = users.map(u => u._id);

      const activeGroupsBySiteId = groupBy(
        await getActiveOrgGroupsByOwners(orgid, currentSchoolYear, userIds, studentGroupId),
        "siteId"
      );
      const activeUserIdsBySiteId = {};
      const archivedUserIdsBySiteId = {};
      siteIds.forEach(sId => {
        activeUserIdsBySiteId[sId] = [];
        archivedUserIdsBySiteId[sId] = [];
      });
      if (role === adminRole) {
        siteIds.forEach(sId => {
          const activeGroups = activeGroupsBySiteId[sId] || [];
          if (activeGroups.length) {
            return;
          }
          const userIdsWithActiveSiteAccess = getUserIdsWithSiteAccess({
            users,
            role,
            siteId: sId,
            isActive: true
          });
          if (!userIdsWithActiveSiteAccess.length) {
            return;
          }
          if (!activeUserIdsBySiteId[sId]) {
            activeUserIdsBySiteId[sId] = [];
          }
          if (!archivedUserIdsBySiteId[sId]) {
            archivedUserIdsBySiteId[sId] = [];
          }
          activeUserIdsBySiteId[sId].push(...userIdsWithActiveSiteAccess);
        });
      }
      Object.values(activeGroupsBySiteId).forEach(activeGroups => {
        activeGroups.forEach(group => {
          const userIdsWithActiveSiteAccess = getUserIdsWithSiteAccess({
            users,
            role,
            siteId: group.siteId,
            isActive: true
          });
          if (!activeUserIdsBySiteId[group.siteId]) {
            activeUserIdsBySiteId[group.siteId] = [];
          }
          if (!archivedUserIdsBySiteId[group.siteId]) {
            archivedUserIdsBySiteId[group.siteId] = [];
          }
          if (role === adminRole) {
            // Coach users have access to current year if they had access to group.siteId in any year, so they are ACTIVE
            activeUserIdsBySiteId[group.siteId].push(...userIdsWithActiveSiteAccess);
          } else {
            if (userIdsWithActiveSiteAccess.includes(group.ownerIds[0])) {
              activeUserIdsBySiteId[group.siteId].push(...group.ownerIds);
            }
            if (group.secondaryTeachers) {
              group.secondaryTeachers
                .filter(st => userIdsWithActiveSiteAccess.includes(st))
                .forEach(teacherId => {
                  activeUserIdsBySiteId[group.siteId].push(teacherId);
                });
            }
          }
          activeUserIdsBySiteId[group.siteId] = uniq(activeUserIdsBySiteId[group.siteId]);
        });
      });
      Object.keys(archivedUserIdsBySiteId).forEach(sId => {
        const userIdsWithAccessToCurrentSite = getUserIdsWithSiteAccess({
          users,
          role,
          siteId: sId,
          isActive: true
        });
        const inactiveUserIds = getUserIdsWithSiteAccess({
          users,
          role,
          siteId: sId,
          isActive: false
        });
        archivedUserIdsBySiteId[sId] = uniq([
          ...archivedUserIdsBySiteId[sId],
          ...difference(userIdsWithAccessToCurrentSite, activeUserIdsBySiteId[sId]),
          ...inactiveUserIds
        ]);
      });
      activeUserIdsByRoleBySiteId[role] = activeUserIdsBySiteId;
      archivedUserIdsByRoleBySiteId[role] = archivedUserIdsBySiteId;
    })
  );

  activeUserIdsByRoleBySiteId.arbitraryIddataAdmin = {
    allSites: arbitraryIddataAdmin.map(u => u._id)
  };
  archivedUserIdsByRoleBySiteId.arbitraryIddataAdmin = {};
  if (Object.keys(archivedUserIdsByRoleBySiteId.arbitraryIdadmin).length) {
    Object.entries({ ...archivedUserIdsByRoleBySiteId.arbitraryIdadmin }).forEach(([sId, userIds]) => {
      archivedUserIdsByRoleBySiteId.arbitraryIdadmin[sId] = uniq([
        ...(Object.keys(userIds).length ? userIds : []),
        ...adminUserIdsWithoutSiteAccess
      ]);
    });
  }
  return {
    teachers: arbitraryIdteacher,
    admins: arbitraryIdadmin,
    dataAdmins: arbitraryIddataAdmin,
    archivedUserIdsByRoleBySiteId,
    activeUserIdsByRoleBySiteId
  };
}

function getRelevantSiteAccess(role, user, currentSchoolYear) {
  return role === coachRole
    ? user.profile.siteAccess
    : user.profile.siteAccess.filter(sa => sa.schoolYear === currentSchoolYear);
}

export const findUsers = async ({ orgid, siteId, firstName, lastName, email, role }) => {
  const keyFirstName = "profile.name.first";
  const keyLastName = "profile.name.last";
  const keyEmail = "emails.address";
  const query = { "profile.orgid": orgid, "profile.siteAccess.role": role };
  if (siteId) {
    query["profile.siteAccess.siteId"] = siteId;
  }
  if (firstName) {
    query[keyFirstName] = { $regex: new RegExp(firstName, "i") };
  }
  if (lastName) {
    query[keyLastName] = { $regex: new RegExp(lastName, "i") };
  }
  if (email) {
    query[keyEmail] = { $regex: new RegExp(escapeRegExp(email), "i") };
  }

  let orgUsers = await Users.find(query, {
    fields: {
      "profile.name": 1,
      "emails.address": 1,
      "profile.orgid": 1,
      "profile.siteAccess": 1
    }
  }).fetchAsync();
  const sites = await Sites.find({ orgid }, { fields: { name: 1, orgid: 1 } }).fetchAsync();
  const currentSchoolYear = await utils.getCurrentSchoolYear(await getMeteorUser());
  orgUsers = orgUsers.filter(user => {
    const siteAccessToUse = getRelevantSiteAccess(role, user, currentSchoolYear);
    return uniq(siteAccessToUse.map(site => site.siteId)).length;
  });
  return orgUsers.map(user => {
    const siteAccessToUse = getRelevantSiteAccess(role, user, currentSchoolYear);
    const userSiteIds = uniq(siteAccessToUse.map(site => site.siteId));
    const filterSites = sites
      .filter(site => userSiteIds.includes(site._id) && (!siteId || site._id === siteId))
      .map(f => ({ siteId: f._id, name: f.name }));
    return {
      ...user,
      filterSites
    };
  });
};

async function getGroupOwners(groupOwnerIds) {
  return Users.find({ _id: { $in: groupOwnerIds } }, { fields: { "profile.name": 1 } }).fetchAsync();
}

async function setCustomDate(userId, customDate = "") {
  const lastModified = await getTimestampInfo(userId, null, "setCustomDate");
  return Users.updateAsync({ _id: userId }, { $set: { "profile.customDate": customDate, lastModified } });
}

export async function updateLoginData(failedLoginEmail = "", id) {
  const userId = id || Meteor?.userId();
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "updateLoginData");
  if (failedLoginEmail.length) {
    const attemptedLoginUser = await Users.findOneAsync(
      { "emails.address": { $regex: new RegExp(`^${escapeRegExp(failedLoginEmail.trim())}$`, "i") } },
      { fields: { loginData: 1 } }
    );
    if (!attemptedLoginUser) {
      return;
    }
    const timestampNow = new Date().getTime();
    let loginData = { lastLogin: null, loginCount: 0, lastFailedLogin: timestampNow };
    if (Object.keys(attemptedLoginUser?.loginData || {}).length) {
      loginData = {
        lastLogin: attemptedLoginUser.loginData.lastLogin,
        loginCount: attemptedLoginUser.loginData.loginCount || 0,
        lastFailedLogin: timestampNow
      };
    }
    await Users.updateAsync(
      { _id: attemptedLoginUser._id },
      { $set: { loginData, "profile.lastModified": lastModified } }
    );
  } else if (userId) {
    const currentUserData = await Users.findOneAsync(
      { _id: userId },
      { fields: { "profile.onboarded": 1, loginData: 1 } }
    );
    const timestampNow = new Date().getTime();
    let loginData = { lastLogin: timestampNow, loginCount: 1, lastFailedLogin: null };
    if (Object.keys(currentUserData?.loginData || {}).length) {
      loginData = {
        lastLogin: timestampNow,
        loginCount: (currentUserData.loginData.loginCount || 0) + 1,
        lastFailedLogin: currentUserData.loginData.lastFailedLogin
      };
    }
    const setQuery = { loginData, "profile.lastModified": lastModified, activityStamp: new Date() };
    if (loginData.loginCount > 0 && !currentUserData?.profile?.onboarded) {
      setQuery["profile.onboarded"] = true;
    }
    await Users.updateAsync({ _id: userId }, { $set: setQuery });
  }
}

export async function getUserAuthInfoByEmail(email = "") {
  const matchedUser = await Users.findOneAsync({ "emails.address": email.toLowerCase() });

  if (!matchedUser) {
    return { isSSOOnlyOrg: false, ssoPortalUrl: "", isDataAdmin: false, isMFARequired: false, isMFAEnabled: false };
  }

  const { orgid, siteAccess } = matchedUser.profile;
  const org = await Organizations.findOneAsync({ _id: orgid });
  const isSSOOnlyOrg = (org?.ssoIssuerOrgId && org?.useSSOOnly) || false;
  let isMFARequired = org?.isMFARequired || false;
  if (!org) {
    isMFARequired = Meteor.settings.public.REQUIRE_MFA_FOR_SPECIAL_ROLES || false;
  }
  const { services: { twoFactorAuthentication } = {} } = matchedUser;
  const isMFAEnabled = (twoFactorAuthentication?.secret && twoFactorAuthentication?.type === "otp") || false;
  const ssoPortalUrl = matchedUser.services?.azureAdB2c?.issuerLogoutRedirect || "";
  const isDataAdmin = !!siteAccess.find(sa => sa.role === ROLE_IDS.dataAdmin);

  return { isSSOOnlyOrg, ssoPortalUrl, isDataAdmin, isMFARequired, isMFAEnabled };
}

const PASSWORD_HISTORY_LIMIT = 10;

async function isReusingPassword(user, newPassword) {
  const currentPasswordHash = user.services?.password?.bcrypt || "";
  const usedPasswordHashes = user.profile?.usedPasswords || [];
  if (currentPasswordHash) {
    usedPasswordHashes.unshift(currentPasswordHash);
  }

  if (!usedPasswordHashes.length) {
    return false;
  }

  return hasAnySameHash(usedPasswordHashes, SHA256(newPassword)); // eslint-disable-line new-cap
}

async function hasAnySameHash(hashes, sha256String) {
  let isUsingSameHash = false;
  // eslint-disable-next-line no-restricted-syntax
  for (const hash of hashes) {
    // eslint-disable-next-line no-await-in-loop
    const isSameHash = await bcryptCompare(sha256String, hash);
    if (isSameHash) {
      isUsingSameHash = true;
      break;
    }
  }

  return isUsingSameHash;
}

async function changePassword(oldPassword, newPassword) {
  const user = await getMeteorUser();

  const isReusingOldPassword = await isReusingPassword(user, newPassword);
  if (isReusingOldPassword) {
    throw new Meteor.Error(403, "The new password was already used in the past");
  }

  const result = await Meteor.callAsync("changePassword", oldPassword, newPassword);

  if (result?.passwordChanged) {
    const currentPasswordHash = user.services.password.bcrypt;
    await savePreviousPasswordToHistory(user, currentPasswordHash);
  }

  return result;
}

export async function savePreviousPasswordToHistory(user, passwordHash) {
  let usedPasswords = user.profile.usedPasswords || [];

  if (!usedPasswords.includes(passwordHash)) {
    usedPasswords.push(passwordHash);
  }
  usedPasswords = usedPasswords.slice(0, PASSWORD_HISTORY_LIMIT - 1); // current password is the 10th one

  await Users.updateAsync(user._id, {
    $set: {
      "profile.usedPasswords": usedPasswords
    }
  });
}

export async function saveLastPasswordChangeDate(userId) {
  const lastModified = await getTimestampInfo(userId, null, "saveLastPasswordChangeDate");
  await Users.updateAsync(userId, {
    $set: {
      "profile.lastPasswordChange": new Date().getTime(),
      "profile.lastModified": lastModified
    }
  });
}

async function resetPassword(token, newPassword) {
  const user = await Users.findOneAsync({ "services.password.reset.token": token });
  if (!user) {
    throw new Meteor.Error(401, "Incorrect token");
  }

  const isReusingOldPassword = await isReusingPassword(user, newPassword);
  if (isReusingOldPassword) {
    throw new Meteor.Error(403, "The new password was already used in the past");
  }

  let result;
  try {
    result = await Meteor.callAsync("resetPassword", token, newPassword);
  } catch (err) {
    if (err.error === "2fa-enabled") {
      result = {
        isMFARequired: true,
        id: user._id
      };
    }
  }
  if (result) {
    const currentPasswordHash = user.services?.password?.bcrypt || "";
    if (currentPasswordHash) {
      await savePreviousPasswordToHistory(user, currentPasswordHash);
    }
  }

  return result;
}

export async function getSortedUserSiteAccess(userId, siteAccess = [], siteIdsInOrg) {
  const groupedSiteAccess = siteAccess.reduce((a, c) => {
    // eslint-disable-next-line no-param-reassign
    a[c.role] ??= [];
    a[c.role].push(c);
    return a;
  }, {});

  const sortedSiteAccess = [];
  Object.values(groupedSiteAccess).forEach(siteAccesses => {
    sortedSiteAccess.push(...siteAccesses.sort((a, b) => b.schoolYear - a.schoolYear));
  });

  const siteAccessWithExistingSites = sortedSiteAccess.filter(
    sa => !([ROLE_IDS.teacher, ROLE_IDS.admin].includes(sa.role) && !siteIdsInOrg.includes(sa.siteId))
  );

  if (!isEqual(siteAccess, siteAccessWithExistingSites)) {
    const lastModified = await getTimestampInfo(userId, null, "getSortedUserSiteAccess");
    await Users.updateAsync(
      { _id: userId },
      { $set: { "profile.siteAccess": siteAccessWithExistingSites, "profile.lastModified": lastModified } }
    );
  }
  return siteAccessWithExistingSites;
}

async function getStudentGroupAssociatedWithUser(userId, userSiteAccess, currentSchoolYear) {
  const query = {
    $or: [{ ownerIds: { $in: [userId] } }, { secondaryTeachers: userId }],
    schoolYear: currentSchoolYear,
    siteId: userSiteAccess?.siteId,
    isActive: true
  };
  return (
    (
      await StudentGroups.aggregate([
        {
          $match: query
        },
        {
          $addFields: {
            normalizedGrade: {
              $switch: {
                branches: [
                  { case: { $eq: ["$grade", "K"] }, then: "00" },
                  { case: { $eq: ["$grade", "HS"] }, then: "09" }
                ],
                default: "$grade"
              }
            }
          }
        },
        { $project: { _id: 1, normalizedGrade: 1, name: 1, orgid: 1 } },
        { $sort: { normalizedGrade: 1, name: 1 } },
        { $limit: 1 }
      ])
    )?.[0] || null
  );
}

if (Meteor.isServer) {
  Meteor.methods({
    async "users:sendEnrollmentEmail"(userId, orgid) {
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      check(orgid, String);
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:sendEnrollmentEmail for this organization");
      }
      check(userId, String);
      await sendEnrollmentEmail(userId);
      return userId;
    },
    async "users:createDataAdminUserAndSendEnrollmentLink"({
      orgid,
      siteId = "allSites",
      email,
      localId = "",
      firstName,
      lastName
    }) {
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin"], {
          userId: this.userId
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:createDataAdminUserAndSendEnrollmentLink");
      }

      const normalizedEmail = normalizeEmail(email);

      const existingUser = await Users.findOneAsync({ "emails.address": normalizedEmail });
      if (localId.length && !(await isLocalIdUniqueInOrg({ localId, orgid, userId: get(existingUser, "_id") }))) {
        throw new Meteor.Error(409, "The provided local Id already belongs to a user that is in this organization");
      }
      if (existingUser) {
        return {
          actionTaken: (await addDataAdminAccessToAdmin({ user: existingUser, localId, orgid, siteId }))
            .wasUserAccessChanged
            ? "updated"
            : null
        };
      }

      await createDataAdminUserAndSendEnrollmentLink({
        orgid,
        siteId,
        email: normalizedEmail,
        localId,
        firstName,
        lastName
      });
      return { actionTaken: "created" };
    },
    async "users:createCoachUserAndSendEnrollmentLink"({ orgid, siteIds, email, firstName, lastName }) {
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:createCoachUserandSendenrollmentlink");
      }

      const normalizedEmail = normalizeEmail(email);

      await createCoachUserAndSendEnrollmentLink({
        orgid,
        siteIds,
        email: normalizedEmail,
        firstName,
        lastName
      });
      return { actionTaken: "created" };
    },
    async "users:createOrUpdateCoachUser"({ orgid, siteIds, email, localId = "", firstName, lastName }) {
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:createOrUpdateCoachUser");
      }

      const normalizedEmail = normalizeEmail(email);

      const exisitingUser = await Users.findOneAsync({
        "emails.address": { $regex: new RegExp(`^${escapeRegExp(normalizedEmail)}$`, "i") }
      });
      if (
        localId.length &&
        !(await isLocalIdUniqueInOrg({ localId, orgid, userId: exisitingUser ? exisitingUser._id : "" }))
      ) {
        throw new Meteor.Error(409, "The provided local Id already belongs to a user that is in this organization");
      }
      if (exisitingUser) {
        return updateCoachUser({ coachEmail: normalizedEmail, localId, orgid, siteIds });
      }
      await createCoachUserAndSendEnrollmentLink({
        orgid,
        siteIds,
        email: normalizedEmail,
        localId,
        firstName,
        lastName
      });
      return { actionTaken: "created" };
    },
    async "users:createSupportUserAndSendEnrollmentLink"({ email, firstName, lastName, organizationIds }) {
      check(email, String);
      check(firstName, String);
      check(lastName, String);
      check(organizationIds, [String]);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(403, "User is not authorized to use users:createSupportUserAndSendEnrollmentLink");
      }

      const normalizedEmail = normalizeEmail(email);

      return createSupportUserAndSendEnrollmentLink({
        email: normalizedEmail,
        firstName,
        lastName,
        organizationIds
      });
    },
    async "users:createUniversalCoachAndSendEnrollmentLink"({ email, firstName, lastName }) {
      check(email, String);
      check(firstName, String);
      check(lastName, String);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(403, "User is not authorized to use users:createUniversalCoachAndSendEnrollmentLink");
      }

      const normalizedEmail = normalizeEmail(email);

      return createUniversalCoachAndSendEnrollmentLink({
        email: normalizedEmail,
        firstName,
        lastName
      });
    },
    async "users:createUniversalDataAdminAndSendEnrollmentLink"({ email, firstName, lastName }) {
      check(email, String);
      check(firstName, String);
      check(lastName, String);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(
          403,
          "User is not authorized to use users:createUniversalDataAdminAndSendEnrollmentLink"
        );
      }

      const normalizedEmail = normalizeEmail(email);

      return createUniversalDataAdminAndSendEnrollmentLink({ email: normalizedEmail, firstName, lastName });
    },
    async "users:createSuperAdminAndSendEnrollmentLink"({ email, firstName, lastName }) {
      check(email, String);
      check(firstName, String);
      check(lastName, String);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(403, "User is not authorized to use users:createSuperAdminAndSendEnrollmentLink");
      }

      const normalizedEmail = normalizeEmail(email);

      return createSuperAdminAndSendEnrollmentLink({ email: normalizedEmail, firstName, lastName });
    },
    async "users:createDownloaderAndSendEnrollmentLink"({ email, firstName, lastName }) {
      check(email, String);
      check(firstName, String);
      check(lastName, String);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(403, "User is not authorized to use users:createDownloaderAndSendEnrollmentLink");
      }

      const normalizedEmail = normalizeEmail(email);

      return createDownloaderAndSendEnrollmentLink({ email: normalizedEmail, firstName, lastName });
    },
    async "users:updateSupportUser"(userId, { organizationAccess }) {
      check(userId, String);
      check(organizationAccess, [String]);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(403, "User is not authorized to use users:updateSupportUser");
      }
      return updateSupportUser(userId, {
        organizationAccess
      });
    },
    async "users:removeUsers"(userIds) {
      check(userIds, [String]);
      if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
        throw new Meteor.Error(403, "User is not authorized to use users:removeUsers");
      }
      const filteredUserIds = userIds.filter(userId => userId !== this.userId); // prevent from removing yourself
      return removeUsers(filteredUserIds);
    },
    async "users:modifyAccess"(userId, siteAccess, role) {
      check(userId, String);
      check(siteAccess, Array);
      check(role, Match.Maybe(String));
      const user = await Users.findOneAsync({ _id: userId });
      const { orgid } = user.profile;
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:modifyAccess");
      }
      return modifySiteAccess({ currentUserId: this.userId, userId, siteAccess, orgid, role });
    },
    async "users:removeCoachUser"(orgid, siteId, coachId) {
      check(orgid, String);
      check(siteId, String);
      check(coachId, String);
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:removeCoachUser");
      }
      return removeCoachUser(coachId, siteId);
    },
    async "users:removeDataAdminUsers"(orgid, dataAdminIds) {
      check(orgid, String);
      check(dataAdminIds, Array);
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:removeDataAdminUsers");
      }
      if (dataAdminIds.includes(this.userId)) {
        throw new Meteor.Error(403, "You can't remove yourself");
      }
      return removeDataAdminUsers(dataAdminIds);
    },
    async "users:authorizationChecks"() {
      if (!this.userId) {
        throw new Meteor.Error(403, "Not logged in");
      }

      const user = await Users.findOneAsync(
        {
          _id: this.userId
        },
        { fields: { "profile.orgid": 1, "profile.lastPasswordChange": 1, "services.twoFactorAuthentication": 1 } }
      );

      return {
        shouldChangePassword: await shouldForceUserToChangePassword(user),
        isConfiguringMFA: isUserConfiguringMFA(user)
      };
    },
    async "users:authorization:getLandingPageForUser"() {
      if (!this.userId) {
        throw new Meteor.Error(403, "Not logged in");
      }
      const user = await Users.findOneAsync({
        _id: this.userId,
        "profile.siteAccess": { $not: { $size: 0 } }
      });
      let isOrgActive = true;
      const userOrgid = user?.profile?.orgid;
      let siteIdsInOrg = [];
      if (userOrgid && userOrgid !== "allOrgs") {
        const organization = await Organizations.findOneAsync({ _id: userOrgid }, { fields: { isActive: 1 } });
        isOrgActive = organization?.isActive;
        siteIdsInOrg = await Sites.find({ orgid: userOrgid }, { fields: { _id: 1 } }).mapAsync(s => s._id);
      }

      if (!user) {
        return "/unauthorized";
      }

      if (user.profile?.lastPasswordChange) {
        if (await shouldForceUserToChangePassword(user)) {
          return {
            isPasswordChangeRequired: true,
            route: "/profile"
          };
        }
      } else {
        await saveLastPasswordChangeDate(user._id);
      }

      const siteAccess = user.profile?.siteAccess || [];
      const sortedUserSiteAccess = await getSortedUserSiteAccess(this.userId, siteAccess, siteIdsInOrg);

      const userSiteAccess = sortedUserSiteAccess.filter(sa => sa.isActive)[0] || {};

      if (!userSiteAccess.role) {
        return "/unauthorized";
      }
      let role = await Roles.findOneAsync({
        _id: userSiteAccess.role
      });
      if (!role) {
        role = {};
        role.name = userSiteAccess.role;
      }
      if (!role?.name) {
        return "/unauthorized";
      }

      if (role.name !== "superAdmin" && role.name !== "support" && role.name !== "universalCoach" && !isOrgActive) {
        return "/organization-is-not-active";
      }
      switch (role.name) {
        case "dataAdmin":
          if (!["none", "allSites"].includes(userSiteAccess.siteId)) {
            return `/data-admin/manage-group/students/${user.profile.orgid}/site/${userSiteAccess.siteId}`;
          }
          return `/data-admin/dashboard/${user.profile.orgid}`;
        case "admin":
          return `/school-overview/${userOrgid}/all/${userSiteAccess.siteId}`;
        case "teacher": {
          const currentSchoolYear = await getCurrentSchoolYear(user, user.profile.orgid);

          const studentGroupAssociatedWithUser = await getStudentGroupAssociatedWithUser(
            user?._id,
            userSiteAccess,
            currentSchoolYear
          );

          if (!studentGroupAssociatedWithUser || userSiteAccess.schoolYear !== currentSchoolYear) {
            return `/site/${userSiteAccess.siteId}/student-groups`;
          }
          return `/${studentGroupAssociatedWithUser.orgid}/site/${userSiteAccess.siteId}/student-groups/${studentGroupAssociatedWithUser._id}`;
        }
        case "universalDataAdmin":
          return "/client-list";
        case "superAdmin":
          return "/clients";
        case "support":
        case "universalCoach":
          return "/support-clients";
        case "downloader":
          return "/download-users";
        default:
          return "/unauthorized";
      }
    },
    async "users:onboardingCompleted"({ completed, firstName, lastName }) {
      check(completed, Boolean);
      check(firstName, String);
      check(lastName, String);
      const userId = getMeteorUserId();
      const lastModified = await getTimestampInfo(userId, null, "users:onboardingCompleted");
      await Users.updateAsync(
        { _id: userId },
        {
          $set: {
            "profile.onboarded": completed,
            "profile.name.first": firstName,
            "profile.name.last": lastName,
            "profile.lastModified": lastModified
          }
        }
      );
      const user = await Users.findOneAsync({ _id: userId }, { fields: { "profile.orgid": 1 } });
      const { orgid } = user.profile;
      const org = await Organizations.findOneAsync({ _id: orgid });
      let isMFARequired = org?.isMFARequired || false;
      if (!org) {
        isMFARequired = Meteor.settings.public.REQUIRE_MFA_FOR_SPECIAL_ROLES || false;
      }
      return { isMFARequired };
    },
    async "users:getUsersInviteEmailStatus"(usersIds, orgid) {
      check(usersIds, Array);
      check(orgid, String);
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "You are not authorized to manage teacher enrollments.");
      }
      return getUserInviteEmailStatus(usersIds);
    },
    async "users:updateUserStandard"({ userId, profile, email }) {
      check(userId, String);
      check(profile, Object);
      check(email, String);
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid: profile.orgid
        }))
      ) {
        throw new Meteor.Error(403, "User is not authorized to use users:updateUserStandard");
      }

      const normalizedEmail = normalizeEmail(email);

      await updateUserStandard({ userId, profile, email: normalizedEmail });
    },
    async "users:changePrimaryRole"({ roleObject, shouldClearSchoolYear = false }) {
      check(roleObject, Object);
      const user = await getMeteorUser();
      if (!user) {
        throw new Meteor.Error(401, "You need to be logged in to change your primary role.");
      }
      const roles = uniq(user.profile.siteAccess.map(sa => sa.role));
      const hasAdminAndDataAdminAccess = roles.includes(ROLE_IDS.admin) && roles.includes(ROLE_IDS.dataAdmin);
      if (hasAdminAndDataAdminAccess) {
        await changePrimaryRole({ roleObject, userId: user._id, shouldClearSchoolYear });
      }
    },
    async "users:removeGroupOwnershipAndDeactivateSiteAccess"(userIds, orgid, siteId) {
      check(userIds, Array);
      check(orgid, String);
      check(siteId, String);
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "You are not authorized to manage teacher enrollments.");
      }
      return removeGroupOwnershipAndDeactivateSiteAccess(userIds, siteId);
    },
    async "users:getManageTeacherData"(orgid, siteId, studentGroupId) {
      check(orgid, String);
      check(siteId, Match.Maybe(String));
      check(studentGroupId, Match.Maybe(String));
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "You are not authorized to view teacher enrollments.");
      }
      return getManageTeacherData({ orgid, siteId, studentGroupId });
    },
    async "users:getTeachers"(orgid, firstName, lastName, email) {
      check(orgid, String);
      check(firstName, String);
      check(lastName, String);
      check(email, String);
      const role = teacherRole;
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "You are not authorized to view teacher enrollments.");
      }
      const dataAdminSiteId = (await getMeteorUser())?.profile?.siteAccess?.find(sa => sa.role === dataAdminRole)
        ?.siteId;
      const siteId = ["none", "allSites"].includes(dataAdminSiteId) ? null : dataAdminSiteId;
      return findUsers({ orgid, siteId, firstName, lastName, email, role });
    },
    async "users:getCoaches"(orgid, firstName, lastName, email) {
      check(orgid, String);
      check(firstName, String);
      check(lastName, String);
      check(email, String);
      const role = coachRole;
      if (
        !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
          userId: this.userId,
          orgid
        }))
      ) {
        throw new Meteor.Error(403, "You are not authorized to view coaches enrollments.");
      }
      const dataAdminSiteId = (await getMeteorUser())?.profile?.siteAccess?.find(sa => sa.role === dataAdminRole)
        ?.siteId;
      const siteId = ["none", "allSites"].includes(dataAdminSiteId) ? null : dataAdminSiteId;
      return findUsers({ orgid, siteId, firstName, lastName, email, role });
    },
    async "users:getGroupOwners"(groupOwnerIds) {
      check(groupOwnerIds, Array);
      return getGroupOwners(groupOwnerIds);
    },
    async "users:changeDefaultSite"(siteId) {
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      check(siteId, String);
      const siteAccessArray = (await getMeteorUser())?.profile?.siteAccess || [];
      const latestAvailableSchoolYear = siteAccessArray
        .filter(elem => elem.siteId === siteId)
        .map(elem => elem.schoolYear);
      const schoolYear = Math.max(...latestAvailableSchoolYear);
      const selectedIndex = siteAccessArray.findIndex(
        curElement => curElement.siteId === siteId && curElement.schoolYear === schoolYear
      );
      const newDefaultSite = siteAccessArray.splice(selectedIndex, 1)[0];
      siteAccessArray.unshift(newDefaultSite);
      const lastModified = await getTimestampInfo(this?.userId, null, "users:changeDefaultSite");
      await Users.updateAsync(
        { _id: this.userId },
        { $set: { "profile.siteAccess": siteAccessArray, "profile.lastModified": lastModified } }
      );
    },
    async "users:hasDashboard"() {
      if (!this.userId) {
        return false;
      }
      return hasDashboard();
    },
    async "users:getSiteAccessByEmail"(email) {
      check(email, String);

      const user = await Users.findOneAsync({ "emails.address": email }, { fields: { "profile.siteAccess": 1 } });

      return user?.profile?.siteAccess;
    },
    async "users:isOwnerOrSecondaryTeacherOfGroup"(studentGroupId) {
      check(studentGroupId, String);

      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }

      const role = await getCurrentLoggedInUserRole();

      return {
        isOwner: !!(await StudentGroups.findOneAsync(
          {
            _id: studentGroupId,
            $or: [{ ownerIds: this.userId }, { secondaryTeachers: this.userId }]
          },
          { fields: { _id: 1 } }
        )),
        userRole: role
      };
    },
    async "users:setCustomDate"(customDate = "") {
      check(customDate, String);

      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }

      return setCustomDate(this.userId, customDate);
    },
    async "users:setSelectedSchoolYear"(schoolYear) {
      check(schoolYear, Match.Maybe(Number));

      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }

      const lastModified = await getTimestampInfo(this?.userId, null, "users:setSelectedSchoolYear");
      if (!schoolYear) {
        return Users.updateAsync(
          { _id: this.userId },
          { $unset: { "profile.selectedSchoolYear": "" }, $set: { "profile.lastModified": lastModified } }
        );
      }

      return Users.updateAsync(
        { _id: this.userId },
        { $set: { "profile.selectedSchoolYear": schoolYear, "profile.lastModified": lastModified } }
      );
    },
    async "users:getUserAuthInfoByEmail"(email = "") {
      check(email, String);

      return getUserAuthInfoByEmail(email);
    },
    async "user:getUserByEnrollTokenOrUserId"(userToken, userId) {
      check(userToken, String);
      check(userId, Match.Maybe(String));

      const userWithToken = await Users.findOneAsync(
        { $or: [{ "services.password.enroll.token": userToken }, { _id: userId }] },
        { fields: { "profile.name": 1 } }
      );

      if (!userWithToken) {
        throw new Meteor.Error(
          404,
          "No pending invite for used link found! Please contact your district Data Admin to resend invite email."
        );
      }

      return userWithToken;
    },
    async "user:getUserData"(userId) {
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }

      check(userId, Match.Maybe(String));

      const selectedUserId = userId || this.userId;
      const matchedUser = await Users.findOneAsync({ _id: selectedUserId });
      if (!matchedUser) {
        throw new Meteor.Error(404, "No user found!");
      }
      const email = matchedUser.emails?.[0]?.address || "N/A";
      const {
        localId: teacherId,
        name: { first: firstName, last: lastName },
        created: { on: accountCreationTimestamp }
      } = matchedUser?.profile || { name: {}, created: {} };
      const siteAccess = matchedUser?.profile.siteAccess.filter(sa => sa.isActive);
      const sites = await Sites.find(
        { _id: { $in: uniq(siteAccess.map(sa => sa.siteId)) } },
        { fields: { name: 1 } }
      ).fetchAsync();
      const siteNameById = getValuesByKey(sites, "_id", "name");
      const userStudentGroups = await StudentGroups.find(
        {
          $or: [{ ownerIds: selectedUserId }, { secondaryTeachers: selectedUserId }],
          schoolYear: await utils.getCurrentSchoolYear(await getMeteorUser()),
          isActive: true
        },
        { fields: { siteId: 1, name: 1 } }
      ).fetchAsync();
      const studentGroupNamesBySiteId = getValuesByKey(userStudentGroups, "siteId", "name", true);
      const rolesWithSiteName = [];
      const studentGroupsBySiteName = {};
      siteAccess.forEach(sa => {
        const roleName = capitalizeFirstLetter(sa.role.slice(11).replace("admin", "coach"));
        if ([ROLE_IDS.teacher, ROLE_IDS.admin, ROLE_IDS.dataAdmin].includes(sa.role)) {
          if (sa.role === ROLE_IDS.teacher && !studentGroupNamesBySiteId[sa.siteId]?.length) {
            return;
          }

          const defaultSiteText = sa.role === ROLE_IDS.dataAdmin ? " - All Sites" : "";
          const siteNameForRole = siteNameById[sa.siteId] ? ` - ${siteNameById[sa.siteId]}` : defaultSiteText;
          rolesWithSiteName.push(`${roleName}${siteNameForRole}`);

          if (sa.role === ROLE_IDS.dataAdmin) {
            return;
          }
          const siteName = siteNameById[sa.siteId];
          const groupName =
            sa.role === ROLE_IDS.teacher
              ? studentGroupNamesBySiteId[sa.siteId] || "No student groups"
              : "All Groups In Site";
          if (!studentGroupsBySiteName[siteName]) {
            studentGroupsBySiteName[siteName] = [];
          }
          if (!studentGroupsBySiteName[siteName].includes(groupName)) {
            studentGroupsBySiteName[siteName].push(groupName);
          }
        } else {
          rolesWithSiteName.push(`${roleName}`);
        }
      });

      const loginData = Object.keys(matchedUser.loginData || {}).length
        ? matchedUser.loginData
        : { lastLogin: null, loginCount: null, lastFailedLogin: null };

      const isSupport = siteAccess.find(sa => sa.role === ROLE_IDS.support);

      let organizationNames;
      if (isSupport) {
        const orgs = await Organizations.find(
          { _id: { $in: matchedUser?.profile?.organizationAccess } },
          { fields: { name: 1 } }
        ).fetchAsync();
        organizationNames = orgs.map(o => o.name);
      } else {
        const orgName =
          (await Organizations.findOneAsync({ _id: matchedUser.profile.orgid }, { fields: { name: 1 } }))?.name ||
          "N/A";
        organizationNames = matchedUser.profile.orgid === "allOrgs" ? ["All Organizations"] : [orgName];
      }

      const { isSSOOnlyOrg, isDataAdmin, isMFARequired, isMFAEnabled } = await getUserAuthInfoByEmail(email);

      return {
        _id: matchedUser._id,
        fullName: `${lastName}, ${firstName}`,
        email,
        teacherId,
        roles: rolesWithSiteName,
        memberSince: new Date(accountCreationTimestamp).toLocaleString(),
        loginData,
        lastPasswordChange: matchedUser?.profile?.lastPasswordChange,
        studentGroupsBySitesPermissions: studentGroupsBySiteName,
        organizationNames,
        isSSOOnlyOrg,
        isDataAdmin,
        isMFARequired,
        isMFAEnabled
      };
    },
    async "users:updateLoginData"({ failedLoginEmail = "", userId } = {}) {
      if (!failedLoginEmail.length && !userId && !this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      check(userId, Match.Maybe(String));
      check(failedLoginEmail, String);

      await updateLoginData(failedLoginEmail, userId);
    },
    async "users:updateActivityStamp"() {
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }

      await Users.updateAsync({ _id: this.userId }, { $set: { activityStamp: new Date() } });
    },
    changeUserPassword(oldPassword, newPassword) {
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }

      check(oldPassword, String);
      check(newPassword, String);

      return changePassword(oldPassword, newPassword);
    },
    async "user:onPasswordChange"(userId) {
      if (!userId && !this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      check(userId, Match.Maybe(String));

      await saveLastPasswordChangeDate(userId || this.userId);
    },
    async "user:sendChangedPasswordEmail"() {
      if (!this.userId) {
        throw new Meteor.Error(403, "No logged in user found!");
      }
      const email = (await getMeteorUser())?.emails?.[0]?.address || "";
      if (!email.length) {
        throw new Meteor.Error(404, "User email not found");
      }

      const { first, last } = (await getMeteorUser())?.profile.name;

      const { token } = Accounts.generateResetToken(this.userId, email, "resetPassword");
      await Email.sendAsync({
        from: "<EMAIL>",
        to: email,
        subject: `Changed Password`,
        html: mapReplaceAll(await Assets.getTextAsync("emailTemplates/changePassword.html"), {
          "{userName}": `${first} ${last}`,
          "{email}": email,
          "{resetPasswordUrl}": Accounts.urls.resetPassword(token)
        })
      });
    },
    async resetUserPassword(token, newPassword) {
      check(token, String);
      check(newPassword, String);

      return resetPassword(token, newPassword);
    },
    async "user:disableMFA"(userId) {
      check(userId, Match.Maybe(String));
      const selectedUserId = userId || this.userId;

      if (!selectedUserId) {
        throw new Meteor.Error(400, "Invalid request to disable MFA");
      }

      await Users.updateAsync(
        { _id: selectedUserId },
        {
          $unset: {
            "services.twoFactorAuthentication": ""
          }
        }
      );
    },
    async "Users:DataAdminsInAllOrganizations"() {
      if (
        !(await auth.hasAccess(["support", "universalCoach", "superAdmin", "universalDataAdmin"], {
          userId: this.userId,
          shouldCheckOrgAccessForSupport: false
        }))
      ) {
        return [];
      }
      return Meteor.users
        .find(
          { "profile.siteAccess.role": "arbitraryIddataAdmin" },
          {
            fields: {
              "profile.orgid": 1,
              "profile.name": 1
            }
          }
        )
        .fetchAsync();
    }
  });
}

export async function hasDashboard() {
  return get(await getMeteorUser(), "profile.hasDashboard");
}

export async function getLoggedInUser() {
  return Users.findOneAsync({ _id: getMeteorUserId() });
}

export default getLoggedInUser;
