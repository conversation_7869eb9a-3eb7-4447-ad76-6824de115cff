import { Meteor } from "meteor/meteor";
import { check, Match } from "meteor/check";
import { Random } from "meteor/random";
import { Email } from "meteor/email";
import { get, pick, uniq, isEmpty, keyBy, range } from "lodash";
import qs from "query-string";

import { ScreeningAssignments } from "../screeningAssignments/screeningAssignments";
import BenchmarkPeriodHelpers from "../benchmarkPeriods/methods";
import { createCurrentBenchmarkWindowForSite, getCurrentBenchmarkWindowWithSiteId } from "../benchmarkWindows/methods";
import { Organizations } from "../organizations/organizations";
import { Students } from "../students/students";
import { AssessmentResults } from "../assessmentResults/assessmentResults";
import { BenchmarkWindows } from "../benchmarkWindows/benchmarkWindows";
import { StudentGroups } from "../studentGroups/studentGroups";
import { StudentsBySkill } from "../studentsBySkill/studentsBySkill";
import { Sites } from "../sites/sites";
import { Users } from "../users/users";
import { getArchivedTeacherOrAdminIds } from "../users/server/methods";
import {
  decryptRosteringSettings,
  getCurrentSchoolYear,
  getMeteorUser,
  getMeteorUserId,
  getValuesByKey,
  mapReplaceAll,
  ninjalog
} from "./utilities";
import * as rolesMethods from "../roles/server/methods";
import * as assessmentResultsMethods from "../assessmentResults/methods";
import {
  AssessmentResultsRestorePoint,
  BenchmarkWindowsRestorePoint,
  StudentGroupsRestorePoint,
  StudentsBySkillRestorePoint,
  StudentsRestorePoint
} from "../restorePointCollections/restorePointCollections";
import * as auth from "../authorization/server/methods";
import { saveGroupData } from "./saveGroupData";
import { getTimestampInfo } from "../helpers/getTimestampInfo";
import { getEdFiCompositeData, getEdFiData, testEdFiConnection } from "../../scripts/edFi/edFi-connect";
import { generateVideoSignedUrl, generateS3SignedUrl } from "./server/s3-bucket";
import { ExternalRosteringAPIManager } from "../../scripts/externalRosteringAPIManager";
import { getDataFromResource } from "../rostering/utils";
import { getListOfStudentIdsFromStudentGroups } from "../studentGroups/utilities";
import { addClassToSite, addStudentToGroup, addTeacher, archiveStudents, moveStudentsBetweenGroups } from "./helpers";
import { insertAssessmentsAndScores } from "../assessmentResults/helpers";
import { StudentGroupEnrollments } from "../studentGroupEnrollments/studentGroupEnrollments";
import { getGrowthDataBySchoolYearByGrade } from "../districtReporting/helpers";
import { AssessmentGrowth } from "../assessmentGrowth/assessmentGrowth";
import { Assessments } from "../assessments/assessments";
import { AssessmentScoresUpload } from "../assessmentScoresUpload/assessmentScoresUpload";
import { AuditLogs } from "../auditLogs/auditLogs";
import { BenchmarkPeriods } from "../benchmarkPeriods/benchmarkPeriods";
import { RosterImports } from "../rosterImports/rosterImports";
import { RosterImportItems } from "../rosterImportItems/rosterImportItems";

async function currentBMPScreeningStatus({ studentGroupId, siteId, grade }) {
  check(studentGroupId, String);
  check(siteId, String);
  check(grade, String);
  // get current bm period
  const currentScreening = {
    hasScreening: false,
    completed: false,
    periodId: ""
  };
  const { orgid } = await StudentGroups.findOneAsync(studentGroupId);
  const benchmarkPeriod = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const benchmarkPeriodId = benchmarkPeriod._id;
  currentScreening.periodId = benchmarkPeriodId;

  const benchmarkAssessmentResult = await AssessmentResults.findOneAsync({
    studentGroupId,
    benchmarkPeriodId,
    type: "benchmark"
  });

  if (!benchmarkAssessmentResult) return currentScreening;

  currentScreening.schoolYear = benchmarkAssessmentResult.schoolYear;
  currentScreening.hasScreening = true;
  currentScreening.completed = benchmarkAssessmentResult.status === "COMPLETED";
  currentScreening.benchmarkAssessmentResultId = benchmarkAssessmentResult._id;

  return currentScreening;
}

async function getPreviousIncompleteScreeningBenchmarkPeriodIds(studentGroupId, grade) {
  const { orgid } = await StudentGroups.findOneAsync({ _id: studentGroupId });
  const currentBp = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
  const currentBpId = currentBp._id;
  return AssessmentResults.find(
    { studentGroupId, benchmarkPeriodId: { $ne: currentBpId }, status: "OPEN", type: "benchmark", grade },
    { fields: { benchmarkPeriodId: 1 } }
  ).mapAsync(ar => ar.benchmarkPeriodId);
}

async function dismissCurrentSkillMessage(entityId, type) {
  let collection;
  let update;
  if (type === "StudentGroup") {
    collection = StudentGroups;
    update = { "currentClasswideSkill.message.dismissed": true };
  } else if (type === "Student") {
    collection = Students;
    update = { "currentSkill.message.dismissed": true };
  } else {
    throw new Meteor.Error(403, "Attempt to remove currentSkill from invalid document type");
  }
  update.lastModified = await getTimestampInfo(getMeteorUserId(), null, "dismissCurrentSkillMessage");
  await collection.updateAsync({ _id: entityId }, { $set: update });
}

export async function testExternalRosteringAPIConnection({
  orgid,
  apiUrl,
  authUrl,
  clientId,
  clientSecret,
  shouldUseScopes = false,
  shouldUseSequentialRequests = false,
  limit = 500,
  schoolYear = "",
  oauthVersion
}) {
  const { rostering } = (await Organizations.findOneAsync({ _id: orgid })) || {};

  if (rostering === "rosterEdFi") {
    return (await testEdFiConnection({ apiUrl, clientId, clientSecret, schoolYear })).catch(error => {
      throw new Meteor.Error(error.statusCode, error.statusMessage, "external");
    });
  }
  if (rostering === "rosterOR") {
    const instance = await new ExternalRosteringAPIManager({
      orgid,
      apiUrl,
      authUrl,
      clientId,
      clientSecret,
      shouldUseScopes,
      shouldUseSequentialRequests,
      limit,
      rosteringType: rostering,
      oauthVersion
    });
    return instance.test().catch(error => {
      throw new Meteor.Error(error.statusCode, error.statusMessage || error.statusCode, "external");
    });
  }

  throw new Meteor.Error("500", "Something went terribly wrong");
}

export async function fetchFilteredUsersForDownloader(data) {
  if (!Object.keys(data || {}).length) {
    return [];
  }

  const { orgIds, userRoles, userStatus, studentCount, isStudentCountGreater } = data;

  const parsedStatus = {
    All: null,
    Active: true,
    Inactive: false
  };

  const userRoleIdByUserRole = {
    "Data Admins": "arbitraryIddataAdmin",
    Coaches: "arbitraryIdadmin",
    Teachers: "arbitraryIdteacher",
    "Support Users": "arbitraryIdsupport"
  };

  const userRoleByRoleId = {
    arbitraryIddataAdmin: "Data Admin",
    arbitraryIdadmin: "Coach",
    arbitraryIdteacher: "Teacher",
    arbitraryIdsupport: "Support User"
  };

  let filteredOrgid = [];
  // eslint-disable-next-line no-restricted-syntax
  for await (const orgid of orgIds) {
    const studentCountForOrg = await StudentGroupEnrollments.find({ orgid }).countAsync();
    if (
      (isStudentCountGreater && parseInt(studentCount) < studentCountForOrg) ||
      (!isStudentCountGreater && parseInt(studentCount) > studentCountForOrg)
    ) {
      filteredOrgid.push(orgid);
    }
  }

  const orgs = await Organizations.find(
    {
      _id: { $in: filteredOrgid }
    },
    { fields: { name: 1, isMFARequired: 1 } }
  ).fetchAsync();
  filteredOrgid = orgs.map(o => o._id);
  const orgById = keyBy(orgs, "_id");

  const query = [
    { "profile.orgid": { $in: filteredOrgid } },
    { "profile.siteAccess.role": { $in: userRoles.map(role => userRoleIdByUserRole[role]).filter(f => f) } }
  ];

  let supportUsersQuery = {};
  if (userRoles.includes("Support Users")) {
    supportUsersQuery["profile.siteAccess.role"] = "arbitraryIdsupport";
    supportUsersQuery["profile.organizationAccess"] = { $in: orgIds };
  }

  if (parsedStatus[userStatus] !== null) {
    const isActiveQuery = { "profile.siteAccess.isActive": parsedStatus[userStatus] ? true : { $ne: true } };
    query.push(isActiveQuery);
    if (userRoles.includes("Support Users")) {
      supportUsersQuery = { ...supportUsersQuery, isActiveQuery };
    }
  }

  const finalQuery = isEmpty(supportUsersQuery) ? { $and: query } : { $or: [supportUsersQuery, { $and: query }] };
  const users = await Users.find(finalQuery, { sort: { "profile.orgid": 1, "profile.name.last": 1 } }).fetchAsync();

  return users.map(
    ({
      profile: {
        name: { first, last },
        siteAccess,
        orgid
      },
      services: { twoFactorAuthentication } = {},
      emails
    }) => ({
      "First Name": first,
      "Last Name": last,
      Email: emails[0]?.address || "N/A",
      Roles: uniq((siteAccess || []).map(sa => sa.role)).map(role => userRoleByRoleId[role]),
      "Archive Status": !(siteAccess || []).find(sa => sa.isActive)?.isActive ? "Inactive" : "Active",
      "District Name": orgById[orgid]?.name || "N/A",
      "District MFA Enabled": orgById[orgid]?.isMFARequired ? "Yes" : "No",
      "User MFA Linked": twoFactorAuthentication?.type ? "Yes" : "No"
    })
  );
}

Meteor.methods({
  /**
   * REFRESH DEMO DATA BACK TO STATE STORED IN BOOTSTRAP COLLECTIONS
   * */
  async refreshDemoDB(orgid) {
    check(orgid, String);
    const { isTestOrg } = (await Organizations.findOneAsync({ _id: orgid }, { isTestOrg: 1 })) || {};
    const sitesForOrg = (await Sites.find({ orgid }, { _id: 1 }).fetchAsync()).map(obj => obj._id);
    try {
      const res = await rolesMethods.getCurrentLoggedInUserRole();

      if (
        res === "superAdmin" &&
        Meteor.settings.CAN_RESET_DEMO &&
        isTestOrg &&
        Meteor.settings.public.ENVIRONMENT !== "PROD"
      ) {
        ninjalog.trace({
          msg: "Restoring data back to stored state -- orgid: ",
          val: orgid,
          context: "bootstrap"
        });
        try {
          await AssessmentResults.removeAsync({ orgid });
          await AssessmentResultsRestorePoint.find({ orgid }).forEachAsync(async doc => {
            await AssessmentResults.insertAsync(doc);
          });

          await BenchmarkWindows.removeAsync({ orgid });
          await BenchmarkWindowsRestorePoint.find({ orgid }).forEachAsync(async doc => {
            await BenchmarkWindows.insertAsync(doc);
          });

          await StudentGroups.removeAsync({ orgid });
          await StudentGroupsRestorePoint.find({ orgid }).forEachAsync(async doc => {
            await StudentGroups.insertAsync(doc);
          });

          await Students.removeAsync({ orgid });
          await StudentsRestorePoint.find({ orgid }).forEachAsync(async doc => {
            await Students.insertAsync(doc);
          });

          await StudentsBySkill.removeAsync({ siteId: { $in: sitesForOrg } });
          await StudentsBySkillRestorePoint.find({ siteId: { $in: sitesForOrg } }).forEachAsync(async doc => {
            await StudentsBySkill.insertAsync(doc);
          });
        } catch (err) {
          throw Error(err);
        }
      } else {
        throw Error("You do not have access to do this super dangerous thing!");
      }
    } catch (err) {
      throw Error(err);
    }
  },
  async saveDemoDB(orgid) {
    check(orgid, String);
    const { isTestOrg } = (await Organizations.findOneAsync({ _id: orgid }, { isTestOrg: 1 })) || {};
    const sitesForOrg = (await Sites.find({ orgid }, { _id: 1 }).fetchAsync()).map(obj => obj._id);
    try {
      const res = await rolesMethods.getCurrentLoggedInUserRole();

      if (
        res === "superAdmin" &&
        Meteor.settings.CAN_RESET_DEMO &&
        isTestOrg &&
        Meteor.settings.public.ENVIRONMENT !== "PROD"
      ) {
        ninjalog.trace({
          msg: "Saving a new restore point -- orgid: ",
          val: orgid,
          context: "bootstrap"
        });
        try {
          await AssessmentResultsRestorePoint.removeAsync({ orgid });
          await AssessmentResults.find({ orgid }).forEachAsync(async doc => {
            await AssessmentResultsRestorePoint.insertAsync(doc);
          });

          await BenchmarkWindowsRestorePoint.removeAsync({ orgid });
          await BenchmarkWindows.find({ orgid }).forEachAsync(async doc => {
            await BenchmarkWindowsRestorePoint.insertAsync(doc);
          });

          await StudentGroupsRestorePoint.removeAsync({ orgid });
          await StudentGroups.find({ orgid }).forEachAsync(async doc => {
            await StudentGroupsRestorePoint.insertAsync(doc);
          });

          await StudentsRestorePoint.removeAsync({ orgid });
          await Students.find({ orgid }).forEachAsync(async doc => {
            await StudentsRestorePoint.insertAsync(doc);
          });

          await StudentGroupsRestorePoint.removeAsync({ orgid });
          await StudentGroups.find({ orgid }).forEachAsync(async doc => {
            await StudentGroupsRestorePoint.insertAsync(doc);
          });

          await StudentsRestorePoint.removeAsync({ orgid });
          await Students.find({ orgid }).forEachAsync(async doc => {
            await StudentsRestorePoint.insertAsync(doc);
          });

          await StudentsBySkillRestorePoint.removeAsync({ siteId: { $in: sitesForOrg } });
          await StudentsBySkill.find({ siteId: { $in: sitesForOrg } }).forEachAsync(async doc => {
            await StudentsBySkillRestorePoint.insertAsync(doc);
          });
        } catch (err) {
          throw Error(err);
        }
      } else {
        throw Error("You do not have access to do this super dangerous thing!");
      }
    } catch (err) {
      throw Error(err);
    }
  },
  async clearDemoDB(orgid) {
    check(orgid, String);
    const sitesForOrg = (await Sites.find({ orgid }, { _id: 1 }).fetchAsync()).map(obj => obj._id);
    try {
      const res = await rolesMethods.getCurrentLoggedInUserRole();

      if (res === "superAdmin") {
        const lastModified = await getTimestampInfo(getMeteorUserId(), orgid, "clearDemoDB");
        ninjalog.trace({
          msg: "Clearing test data from orgid:  -- orgid: ",
          val: orgid,
          context: "bootstrap"
        });
        const schoolYear = await getCurrentSchoolYear(await getMeteorUser(), orgid);
        await AssessmentResults.removeAsync({ orgid, schoolYear });
        await BenchmarkWindows.removeAsync({ orgid, schoolYear });
        await StudentGroups.updateAsync(
          { orgid, schoolYear },
          {
            $unset: {
              history: "",
              currentClasswideSkill: "",
              currentAssessmentResultIds: "",
              individualInterventionQueue: ""
            },
            $set: { lastModified }
          },
          { multi: true }
        );
        await Students.updateAsync(
          { orgid, schoolYear },
          {
            $unset: {
              history: "",
              currentSkill: "",
              currentAssessmentResultIds: ""
            },
            $set: { lastModified }
          },
          { multi: true }
        );
        await StudentsBySkill.updateAsync(
          { siteId: { $in: sitesForOrg } },
          {
            $set: {
              studentsBelowInstructionalTarget: [],
              studentsBelowMasteryTarget: [],
              studentsWithoutSkillHistory: []
            }
          },
          { multi: true }
        );
      } else {
        throw Error("You do not have access to do this super dangerous thing!");
      }
    } catch (err) {
      throw Error(err);
    }
  },
  async removeOrg(orgid) {
    check(orgid, String);
    if (orgid === Meteor.settings.public.DATA_COPY_SOURCE_ORG_ID || orgid === "LyGDeCXKYkoiB9SCW") {
      throw new Meteor.Error("removeOrg", "Cannot remove Data Template organization");
    }

    try {
      const role = await rolesMethods.getCurrentLoggedInUserRole();

      if (role === "superAdmin") {
        ninjalog.trace({
          msg: "Removing organization -- orgid: ",
          val: orgid,
          context: "bootstrap"
        });
        const siteIds = (await Sites.find({ orgid }, { _id: 1 }).fetchAsync()).map(obj => obj._id);

        await AssessmentResults.removeAsync({ orgid });
        await AssessmentScoresUpload.removeAsync({ orgid });
        await AuditLogs.removeAsync({ orgid });
        await BenchmarkWindows.removeAsync({ orgid });
        await Organizations.removeAsync({ _id: orgid });
        await RosterImports.removeAsync({ orgid });
        await RosterImportItems.removeAsync({ orgid });
        await Sites.removeAsync({ orgid });
        await StudentGroupEnrollments.removeAsync({ orgid });
        await StudentGroups.removeAsync({ orgid });
        await Students.removeAsync({ orgid });
        await StudentsBySkill.removeAsync({ siteId: { $in: siteIds } });
        await Users.removeAsync({ "profile.orgid": orgid });
      } else {
        throw Error("You do not have access to do this super dangerous thing!");
      }
    } catch (err) {
      throw Error(err);
    }
  },
  /** ****************************************************************************
   * Build up new db items required to an administer screening assessments
   ***************************************************************************** */
  async setUpNewScreeningPeriod({ studentGroupId, siteId, grade, orgid }) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(studentGroupId, String);
    check(orgid, String);
    check(siteId, String);
    check(grade, String);

    const user = await getMeteorUser();
    const { userId } = this;
    const schoolYear = await getCurrentSchoolYear(user, orgid);

    if (await auth.hasSiteAccess(userId, siteId)) {
      // check to see if there is a benchmark window
      let mostRecentBMWindow = await getCurrentBenchmarkWindowWithSiteId({
        orgid,
        siteId,
        schoolYear
      });
      // if not then create it
      if (!mostRecentBMWindow) {
        mostRecentBMWindow = await createCurrentBenchmarkWindowForSite({
          orgid,
          siteId,
          userId
        });
      }

      const bmp = await BenchmarkPeriodHelpers.getBenchmarkPeriodByDate({ orgid });
      // get current bm period
      const benchmarkPeriodId = bmp._id;

      // Assessment Results - refactor from benchmark schedules
      const assResult =
        (await assessmentResultsMethods.mostRecentOpenBenchmarkAssessmentResultForStudentGroup({
          studentGroupId,
          schoolYear,
          benchmarkPeriodId
        })) ||
        (await assessmentResultsMethods.createNewBenchmarkAssessmentResultForStudentGroup({
          studentGroupId,
          userId: this.userId,
          schoolYear,
          benchmarkPeriodId,
          grade,
          orgid
        }));

      // create scores
      // get the assessents for grade, peroid
      const screeningAssignments = await ScreeningAssignments.findOneAsync({
        grade,
        benchmarkPeriodId
      });
      const { assessmentIds } = screeningAssignments;
      const studentIdsInThisStudentGroup = await getListOfStudentIdsFromStudentGroups([studentGroupId]);
      const scores = assessmentIds.reduce((scoreList, assessmentId) => {
        const studentScores = studentIdsInThisStudentGroup.map(studentId => ({
          _id: Random.id(),
          assessmentId,
          orgid,
          siteId,
          status: "STARTED",
          studentId
        }));
        return [...scoreList, ...studentScores];
      }, []);
      // inserts/overwrites scores on the assessmentResult.. not sure if this is the best
      // idea yet, but we're going to leave it.  There should only be one assessmentResult
      // per studentGroup for benchmarking per benchmark period.
      await insertAssessmentsAndScores({
        assessmentResultId: assResult._id,
        assessmentIds,
        scores
      });

      return true;
    }
    throw new Meteor.Error(403, "Access denied! You don't have permissions to do this.");
  },
  async "ScreeningHelpers:currentBMPScreeningStatus"({ studentGroupId, siteId, grade }) {
    check(studentGroupId, String);
    check(siteId, String);
    check(grade, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return currentBMPScreeningStatus({
      studentGroupId,
      siteId,
      grade
    });
  },
  async "ScreeningHelpers:getPreviousIncompleteScreeningBenchmarkPeriodIds"(studentGroupId, grade) {
    check(studentGroupId, String);
    check(grade, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return getPreviousIncompleteScreeningBenchmarkPeriodIds(studentGroupId, grade);
  },
  async DismissCurrentSkillMessage(entityId, type) {
    check(entityId, String);
    check(type, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    return dismissCurrentSkillMessage(entityId, type);
  },
  // Flag for removal option is hidden for now - SPRIN-1029
  // TODO: If this code is uncommented it needs security improvements
  // 'flagStudentForRemoval'({ studentGroupId, studentId, unflag = false }) {
  //   check(studentGroupId, String);
  //   check(studentId, String);
  //   if (!this.userId) { throw new Meteor.Error(403, 'No logged in user found!'); }
  //   if (unflag) {
  //     StudentGroups.update({ _id: studentGroupId }, { $pull: { flaggedForRemovalStudentIds: studentId } });
  //   } else {
  //     StudentGroups.update({ _id: studentGroupId }, { $addToSet: { flaggedForRemovalStudentIds: studentId } });
  //   }
  // },
  async moveStudentsBetweenGroups({ students, previousGroupId, nextGroupId, isUnarchiveOperation = false }) {
    check(students, [Object]);
    check(previousGroupId, String);
    check(nextGroupId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const previousGroup = previousGroupId ? await StudentGroups.findOneAsync(previousGroupId) : null;
    const nextGroup = await StudentGroups.findOneAsync(nextGroupId);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid: nextGroup.orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use move students between groups for selected sites.");
    }

    return moveStudentsBetweenGroups({
      students,
      previousGroup,
      nextGroup,
      isUnarchiveOperation
    });
  },
  async archiveStudents({ studentIds, studentGroupId }) {
    check(studentIds, [String]);
    check(studentGroupId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const studentGroup = await StudentGroups.findOneAsync(studentGroupId);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid: studentGroup.orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to archive students for selected sites.");
    }

    return archiveStudents({ studentIds, studentGroup });
  },
  async addStudentToGroup({ student, studentGroupId }) {
    check(student, Object);
    check(studentGroupId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const studentGroup = await StudentGroups.findOneAsync(studentGroupId);

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid: studentGroup.orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use add student to group for selected sites.");
    }
    await addStudentToGroup({ student, studentGroup });
    return true;
  },
  async addClassToSite({ studentGroup, teacher, orgid, siteId }) {
    check(studentGroup, Object);
    check(teacher, Object);
    check(orgid, String);
    check(siteId, String);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use addClassToSite for selected site.");
    }

    return addClassToSite({ studentGroup, teacher, orgid, siteId });
  },
  async deactivateOrganization(orgid) {
    check(orgid, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error(403, "User is not authorized to use deactivateOrganization.");
    }

    return deactivateOrganization(orgid, this.userId);
  },
  async reactivateOrganization(orgid) {
    check(orgid, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (!(await auth.hasAccess(["superAdmin"], { userId: this.userId }))) {
      throw new Meteor.Error(403, "User is not authorized to use reactivateOrganization.");
    }

    return reactivateOrganization(orgid, this.userId);
  },
  async saveGroupData({
    studentGroupId,
    newTeacherId,
    orgid,
    siteId,
    studentGroupName,
    secondaryTeachers,
    grade,
    hasGradeChanged,
    hasPrimaryTeacherChanged,
    sectionId
  }) {
    check(studentGroupId, String);
    check(newTeacherId, String);
    check(orgid, String);
    check(siteId, String);
    check(studentGroupName, String);
    check(secondaryTeachers, [String]);
    check(grade, String);
    check(hasGradeChanged, Boolean);
    check(hasPrimaryTeacherChanged, Boolean);
    check(sectionId, Match.Maybe(String));

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use changeGroupTeacher for selected sites.");
    }
    const schoolYear = await StudentGroups.findOneAsync({ _id: studentGroupId }, { fields: { schoolYear: 1 } });
    if (
      sectionId &&
      (await StudentGroups.findOneAsync({ orgid, sectionId, schoolYear, _id: { $ne: studentGroupId } }))
    ) {
      throw new Meteor.Error(409, "Section ID is a duplicate!");
    }
    return saveGroupData({
      hasPrimaryTeacherChanged,
      studentGroupId,
      newTeacherId,
      secondaryTeachers,
      studentGroupName,
      hasGradeChanged,
      grade,
      siteId,
      sectionId
    });
  },
  async addTeacher({ teacher, orgid, siteId, schoolYear }) {
    check(teacher, Object);
    check(orgid, String);
    check(siteId, String);
    check(schoolYear, Number);
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use addTeacher for selected sites.");
    }
    return addTeacher({ teacher, orgid, siteId, schoolYear });
  },
  async updateStudentData(studentGroupId, studentData) {
    check(studentGroupId, String);
    check(
      studentData,
      Match.ObjectIncluding({
        firstName: String,
        lastName: String,
        birthDate: Match.Maybe(String),
        birthDateTimeStamp: Match.Maybe(Number),
        localId: String,
        stateId: String
      })
    );

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    const { orgid, schoolYear } = await StudentGroups.findOneAsync(studentGroupId, {
      fields: { orgid: 1, schoolYear: 1 }
    });

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to update students for selected group.");
    }

    return updateStudentData(studentData, { orgid, schoolYear });
  },
  async sendEmailForNewRosterFile({ orgid, filename, domain }) {
    check(orgid, String);
    check(filename, String);
    check(domain, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to upload roster files.");
    }

    const organization = await Organizations.findOneAsync(orgid, { fields: { name: 1 } });
    const user = await Meteor.users.findOneAsync(this.userId, { fields: { "profile.name": 1, "emails.address": 1 } });
    const userFullName = `${user.profile.name.first} ${user.profile.name.last}`;
    const envEmails = Meteor.settings.CUSTOM_ROSTERING_EMAILS || [];
    const userEmail = user.emails[0].address;

    const replaceMap = {
      "{orgName}": organization.name,
      "{filename}": filename,
      "{userFullName}": userFullName,
      "{userEmail}": userEmail,
      "{domain}": domain
    };

    await Email.sendAsync({
      from: "<EMAIL>",
      to: envEmails.length ? envEmails : "<EMAIL>",
      subject: `New SpringMath Roster file from ${organization.name}`,
      html: mapReplaceAll(await Assets.getTextAsync("emailTemplates/newRosterFile.html"), replaceMap)
    });
  },
  async sendEmailForRosterImportErrors({ orgid, rosterImportType, importErrors }) {
    check(orgid, String);
    check(rosterImportType, String);
    check(importErrors, Array);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use sendEmailForRosterImportErrors.");
    }

    return sendEmailForRosterImportErrors({ orgid, rosterImportType, importErrors });
  },
  async checkIfEmailAlreadyExists(email, orgid) {
    check(email, String);
    check(orgid, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to check if email already exists.");
    }

    const existingUser = await Users.findOneAsync(
      { "emails.address": email },
      { fields: { emails: 1, "profile.name": 1 } }
    );
    if (!existingUser) {
      return existingUser;
    }

    const archivedTeacherIds = await getArchivedTeacherOrAdminIds([existingUser._id], orgid);
    if (archivedTeacherIds.length) {
      return { ...existingUser, isArchived: true };
    }
    return existingUser;
  },
  async getSchoolYearsWithAssessmentResults({ orgIds, type }) {
    check(orgIds, [String]);
    check(type, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid: orgIds[0]
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use getSchoolYearsWithAssessmentResults.");
    }
    return getSchoolYearsWithAssessmentResults({ orgIds, type });
  },
  async getEdFiData({ orgid, itemsToFetch, fields = [], query = {} }) {
    check(orgid, String);
    check(itemsToFetch, String);
    check(fields, Array);
    check(query, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use getEdFiData.");
    }

    return getDataFromResource({
      orgid,
      itemsToFetch,
      query,
      fields,
      useComposites: false
    });
  },
  async getRosteringData({
    orgid,
    resource,
    fields = [],
    query = {},
    queryText = "",
    temporaryFilters = {},
    useComposites = false
  }) {
    check(orgid, String);
    check(resource, String);
    check(fields, Array);
    check(query, Object);
    check(temporaryFilters, Object);
    check(useComposites, Boolean);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use getRosteringData.");
    }

    const { rostering, rosteringSettings } =
      (await Organizations.findOneAsync({ _id: orgid }, { fields: { rostering: 1, rosteringSettings: 1 } })) || {};
    const { apiUrl, authUrl, clientId, clientSecret } = decryptRosteringSettings(rosteringSettings);
    const {
      shouldUseScopes = false,
      shouldIgnoreEnrollmentStartDate = false,
      shouldIgnoreEnrollmentEndDate = false,
      shouldUseSequentialRequests = false,
      userIdentifiers = {},
      limit
    } = rosteringSettings;

    if (rostering === "rosterEdFi") {
      try {
        let data;
        const { schoolYear } = rosteringSettings;
        if (useComposites) {
          const { data: resp } = await getEdFiCompositeData({
            apiUrl,
            clientId,
            clientSecret,
            schoolYear,
            limit,
            itemsToFetch: resource,
            query,
            temporaryFilters,
            useComposites
          });
          data = resp;
        } else {
          const { data: resp } = await getEdFiData({
            apiUrl,
            clientId,
            clientSecret,
            schoolYear,
            limit,
            itemsToFetch: resource,
            query
          });
          data = resp;
        }
        if (fields.length) {
          return data.map(elem => pick(elem, fields));
        }
        return data;
      } catch (e) {
        throw new Meteor.Error(
          e.statusCode || e.response?.status || 500,
          e.statusMessage || e.response?.statusText || "Problem with your Rostering server. Try again later."
        );
      }
    }
    if (rostering === "rosterOR") {
      const externalRosteringAPIManager = await new ExternalRosteringAPIManager({
        orgid,
        apiUrl,
        authUrl,
        clientId,
        clientSecret,
        shouldUseScopes,
        shouldUseSequentialRequests,
        shouldIgnoreEnrollmentStartDate,
        shouldIgnoreEnrollmentEndDate,
        userIdentifiers,
        limit
      });
      // eslint-disable-next-line no-use-before-define
      const externalAPIManagerFunctionsByResource = externalRosteringAPIManager.mapFunctionsByResource({
        temporaryFilters,
        queryText,
        query
      });
      let response;
      if (externalAPIManagerFunctionsByResource[resource]) {
        response = await externalAPIManagerFunctionsByResource[resource]().catch(error => {
          throw new Meteor.Error(error.statusCode, error.statusMessage || error.statusCode, "external");
        });
      } else {
        // TODO to be removed if all externalAPIManagerFunctionsByResource[resource]
        response = await externalRosteringAPIManager.get({
          resourceUrl: getRosteringResourceUrl({
            resource,
            query
          })
        });
      }
      // TODO extend field translation map
      const processedData = processExternalRosteringResource({
        resourceName: resource,
        rosteringType: rostering,
        // response as array may be nested when using some One Roster endpoints
        resourceData: Array.isArray(response) ? response.flat(1) : response.data
      });
      if (fields.length) {
        return processedData.map(elem => pick(elem, fields));
      }
      return processedData;
    }

    throw new Meteor.Error("500", "Something went terribly wrong");
  },
  async testExternalRosteringAPIConnection({
    orgid,
    apiUrl,
    authUrl = "",
    clientId,
    clientSecret,
    shouldUseScopes = false,
    schoolYear
  }) {
    check(orgid, String);
    check(apiUrl, String);
    check(authUrl, String);
    check(clientId, String);
    check(clientSecret, String);
    check(shouldUseScopes, Boolean);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use testExternalRosteringAPIConnection.");
    }
    return testExternalRosteringAPIConnection({
      orgid,
      apiUrl,
      authUrl,
      clientId,
      clientSecret,
      shouldUseScopes,
      schoolYear
    });
  },
  async getNamesForMissingFilterIds({ orgid, schoolIds, teacherIds, classesIds }) {
    check(orgid, String);
    check(schoolIds, Array);
    check(teacherIds, Array);
    check(classesIds, Array);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "universalDataAdmin", "dataAdmin"], {
        userId: this.userId,
        orgid
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use getNamesForMissingFilterIds.");
    }
    return getNamesForMissingFilterIds({ orgid, schoolIds, teacherIds, classesIds });
  },
  async fetchFilteredUsersForDownloader(data = {}) {
    check(data, Object);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    if (
      !(await auth.hasAccess(["superAdmin", "downloader"], {
        userId: this.userId
      }))
    ) {
      throw new Meteor.Error(403, "User is not authorized to use fetchFilteredUsersForDownloader.");
    }

    return fetchFilteredUsersForDownloader(data);
  },
  async getGrowthDataBySchoolYearByGradeProgramEvaluation(schoolYear, siteId, orgid) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(schoolYear, Number);
    check(siteId, String);
    check(orgid, String);

    const studentGroupProject = {
      grade: 1,
      schoolYear: 1,
      siteId: 1,
      "history.assessmentId": 1,
      "history.benchmarkPeriodId": 1,
      "history.enrolledStudentIds": 1,
      "history.targets": 1,
      "history.type": 1,
      "history.whenEnded.on": 1,
      "history.assessmentResultMeasures.assessmentId": 1,
      "history.assessmentResultMeasures.medianScore": 1,
      "history.assessmentResultMeasures.numberMeetingTarget": 1,
      "history.assessmentResultMeasures.studentScores": 1,
      "history.assessmentResultMeasures.targetScores": 1,
      "history.assessmentResultMeasures.totalStudentsAssessed": 1,
      "currentClasswideSkill.assessmentId": 1,
      hasCompletedCWI: 1,
      individualInterventionQueue: 1,
      ownerIds: 1,
      secondaryTeachers: 1
    };

    const currentAndTwoPriorSchoolYears = range(schoolYear - 2, schoolYear + 1);
    const studentGroupsAggregate = [];
    // Data structure for studentGroupsAggregate
    // [{ schoolYear: 2022, sites: [{ siteId: "siteId1", documents: ["student groups for siteId1"] }, ...] }, ...];
    // eslint-disable-next-line no-restricted-syntax
    for await (const year of currentAndTwoPriorSchoolYears) {
      const studentGroupsForSchoolYear = await StudentGroups.aggregate([
        { $match: { orgid, isActive: true, schoolYear: year, siteId } },
        { $project: studentGroupProject },
        { $group: { _id: "$siteId", documents: { $push: "$$ROOT" } } }
      ]);
      studentGroupsAggregate.push({ schoolYear: year, sites: studentGroupsForSchoolYear });
    }

    const studentGroupsBySiteIdBySchoolYear = {};
    currentAndTwoPriorSchoolYears.forEach(year => {
      studentGroupsBySiteIdBySchoolYear[year] = keyBy(
        studentGroupsAggregate.find(sg => sg.schoolYear === year)?.sites || {},
        "_id"
      );
    });

    const assessmentNameById = getValuesByKey(
      await Assessments.find({}, { fields: { name: 1 } }).fetchAsync(),
      "_id",
      "name"
    );
    const bmPeriods = await BenchmarkPeriods.find({}, { fields: { created: 0, lastModified: 0 } }).fetchAsync();
    const assessmentGrowth = await AssessmentGrowth.find().fetchAsync();

    const growthDataBySchoolYearByGrade = getGrowthDataBySchoolYearByGrade({
      studentGroupsAggregate,
      studentGroupsBySiteIdBySchoolYear,
      assessmentGrowth,
      assessmentNameById,
      bmPeriods
    });

    return growthDataBySchoolYearByGrade;
  },
  async getOrgidFromSiteIdOrStudentGroupId(siteId, studentGroupId) {
    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }
    check(siteId, Match.Maybe(String));
    check(studentGroupId, Match.Maybe(String));

    return (
      (await Sites.findOneAsync({ _id: siteId }, { fields: { orgid: 1 } }))?.orgid ||
      (await StudentGroups.findOneAsync({ _id: studentGroupId }, { fields: { orgid: 1 } }))?.orgid
    );
  },

  async "Utilities:getVideoSignedUrl"(measureNumber) {
    check(measureNumber, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    try {
      return await generateVideoSignedUrl(measureNumber);
    } catch (error) {
      throw new Meteor.Error(500, `Failed to generate signed URL: ${error.message}`);
    }
  },

  async "Utilities:getIncrementalRehearsalSignedUrl"(skillName, filename) {
    check(skillName, String);
    check(filename, String);

    if (!this.userId) {
      throw new Meteor.Error(403, "No logged in user found!");
    }

    try {
      const objectPath = `protocol-images/Incremental-Rehearsal/${skillName}/${filename}`;
      return await generateS3SignedUrl(objectPath);
    } catch (error) {
      throw new Meteor.Error(500, `Failed to generate signed URL: ${error.message}`);
    }
  }
});

function processExternalRosteringResource({ resourceName, rosteringType, resourceData }) {
  const rosteringPropertyMap = {
    rosterOR: {
      schools: {
        sourcedId: "schoolId",
        identifier: "ncesSchoolId",
        name: "nameOfInstitution"
      },
      classes: {
        grades: "grades",
        sourcedId: "sourcedId"
      }
    },
    rosterEdFi: {}
  };

  const propertyMap = get(rosteringPropertyMap, `${rosteringType}.${resourceName}`, {});

  return resourceData
    .map(datum => {
      if (!datum || !Object.keys(datum).length) {
        return null;
      }
      const obj = { ...datum };
      Object.entries(propertyMap).forEach(([key, value]) => {
        obj[value] = datum[key];
      });
      return obj;
    })
    .filter(Boolean);
}

function getSchoolYearsWithAssessmentResults({ orgIds, type }) {
  return AssessmentResults.rawCollection().distinct("schoolYear", {
    orgid: { $in: orgIds },
    ...(type !== "benchmark" ? { type } : { type: { $in: ["benchmark", "classwide"] } }),
    status: "COMPLETED"
  });
}

function getRosteringResourceUrl({ resource, query = {} }) {
  return qs.stringifyUrl({ url: `/${resource}`, query });
}

async function updateStudentData(studentData, { orgid, schoolYear }) {
  const query = {
    _id: { $ne: studentData._id },
    orgid,
    schoolYear
  };
  const studentWithLocalId = await Students.findOneAsync({
    ...query,
    "identity.identification.localId": studentData.localId
  });
  if (studentWithLocalId) {
    const studentFullName = `${studentWithLocalId.identity.name.firstName} ${studentWithLocalId.identity.name.lastName}`;
    throw new Meteor.Error("409", `Student (${studentFullName}) with this local ID already exists`);
  }
  const studentWithStateId = await Students.findOneAsync({
    ...query,
    "identity.identification.stateId": studentData.stateId
  });
  if (studentWithStateId) {
    const studentFullName = `${studentWithStateId.identity.name.firstName} ${studentWithStateId.identity.name.lastName}`;
    throw new Meteor.Error("409", `Student (${studentFullName}) with this state ID already exists`);
  }
  const lastModified = await getTimestampInfo(getMeteorUserId(), null, "updateStudentData");
  const setObject = {
    "identity.name.firstName": studentData.firstName,
    "identity.name.lastName": studentData.lastName,
    "identity.identification.localId": studentData.localId,
    "identity.identification.stateId": studentData.stateId,
    lastModified
  };
  const unsetObject = {};
  if (studentData.birthDate) {
    setObject["demographic.birthDate"] = studentData.birthDate;
    setObject["demographic.birthDateTimeStamp"] = studentData.birthDateTimeStamp;
  } else {
    unsetObject["demographic.birthDate"] = "";
    unsetObject["demographic.birthDateTimeStamp"] = "";
  }
  return Students.updateAsync(
    { _id: studentData._id },
    {
      $set: setObject,
      ...(Object.keys(unsetObject).length ? { $unset: unsetObject } : {})
    }
  );
}

async function deactivateOrganization(orgid, byUserId) {
  const byDateOn = await getTimestampInfo(byUserId, orgid, "deactivateOrganization");

  return Organizations.updateAsync(orgid, {
    $set: {
      isActive: false,
      lastModified: byDateOn
    }
  });
}

async function reactivateOrganization(orgid, byUserId) {
  const byDateOn = await getTimestampInfo(byUserId, orgid, "reactivateOrganization");

  return Organizations.updateAsync(orgid, {
    $set: {
      isActive: true,
      lastModified: byDateOn
    }
  });
}

export async function sendEmailForRosterImportErrors({ orgid, rosterImportType, importErrors }) {
  const organization = await Organizations.findOneAsync(orgid, { fields: { name: 1 } });
  const users = await Meteor.users
    .find(
      { "profile.orgid": orgid, "profile.siteAccess.role": "arbitraryIddataAdmin" },
      { fields: { "emails.address": 1 } }
    )
    .fetchAsync();

  const envEmails = Meteor.settings.CUSTOM_ROSTERING_EMAILS || [];
  const userEmails = envEmails.length ? envEmails : users.map(user => user.emails[0].address);

  if (!userEmails.length) {
    ninjalog.warning({
      msg: `No Data Admins' emails found for the ${organization.name} organization`
    });
    return;
  }

  const emailSubject = `Action required: ${rosterImportType} import failed for ${organization.name}`;

  const replaceMap = {
    "{orgName}": organization.name,
    "{rosterImportType}": rosterImportType,
    "{importErrors}": importErrors.map(error => error.replace(/\n/gi, "<br/>")).join("<br/><br/>"),
    "{emailSubject}": emailSubject
  };

  try {
    await Email.sendAsync({
      from: "<EMAIL>",
      bcc: userEmails,
      subject: emailSubject,
      html: mapReplaceAll(await Assets.getTextAsync("emailTemplates/rosterImportErrors.html"), replaceMap)
    });
  } catch (e) {
    ninjalog.error({
      msg: `Error while sending an email in ${organization.name} organization`,
      val: e,
      context: "roster-import"
    });
  }
}

export async function sendEmailForNewYearAccessReviewReminder({ orgid, rosterPageLink }) {
  const organization = await Organizations.findOneAsync(orgid, { fields: { name: 1 } });
  const users = await Meteor.users
    .find(
      { "profile.orgid": orgid, "profile.siteAccess.role": "arbitraryIddataAdmin" },
      { fields: { "emails.address": 1 } }
    )
    .fetchAsync();

  const envEmails = Meteor.settings.CUSTOM_ROSTERING_EMAILS || [];
  const userEmails = envEmails.length ? envEmails : users.map(user => user.emails[0].address);

  if (!userEmails.length) {
    ninjalog.warning({
      msg: `No Data Admins' emails found for the ${organization.name} organization`
    });
    return;
  }

  const emailSubject = `Reminder to review access for the new school year for ${organization.name}`;

  const replaceMap = {
    "{orgName}": organization.name,
    "{reviewPageLink}": rosterPageLink,
    "{emailSubject}": emailSubject
  };

  await Email.sendAsync({
    from: "<EMAIL>",
    bcc: userEmails,
    subject: emailSubject,
    html: mapReplaceAll(await Assets.getTextAsync("emailTemplates/newYearAccessReviewReminder.html"), replaceMap)
  });
}

async function getNamesForMissingFilterIds({ orgid, schoolIds, teacherIds, classesIds }) {
  const sites = {};
  (await Sites.find({ orgid, "stateInformation.schoolNumber": { $in: schoolIds } }).fetchAsync()).forEach(elem => {
    sites[elem.stateInformation.schoolNumber] = elem.name;
  });

  const users = {};
  (await Users.find({ "profile.orgid": orgid, "profile.localId": { $in: teacherIds } }).fetchAsync()).forEach(elem => {
    users[elem.profile.localId] = `${elem.profile.name.first} ${elem.profile.name.last}`;
  });

  const studentGroups = {};
  (await StudentGroups.find({ orgid, sectionId: { $in: classesIds } }).fetchAsync()).forEach(elem => {
    studentGroups[elem.sectionId] = elem.name;
  });

  return {
    schools: schoolIds.map(id => ({ id, name: sites[id] })),
    teachers: teacherIds.map(id => ({ id, name: users[id] })),
    classes: classesIds.map(id => ({ id, name: studentGroups[id] }))
  };
}
